<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- SaaS Plan List View -->
    <record id="view_saas_plan_list" model="ir.ui.view">
        <field name="name">saas.plan.list</field>
        <field name="model">saas.plan</field>
        <field name="type">list</field>
        <field name="arch" type="xml">
            <list string="SaaS Plans">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="monthly_price"/>
                <field name="yearly_price"/>
                <field name="max_users"/>
                <field name="max_storage_gb"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <!-- SaaS Plan Form View -->
    <record id="view_saas_plan_form" model="ir.ui.view">
        <field name="name">saas.plan.form</field>
        <field name="model">saas.plan</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form string="SaaS Plan">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <field name="active" widget="boolean_button" 
                               options="{'terminology': 'archive'}"/>
                    </div>
                    <group>
                        <group name="basic_info" string="Basic Information">
                            <field name="name"/>
                            <field name="sequence"/>
                            <field name="description"/>
                        </group>
                        <group name="pricing" string="Pricing">
                            <field name="currency_id"/>
                            <field name="monthly_price"/>
                            <field name="yearly_price"/>
                            <field name="yearly_discount" readonly="1"/>
                        </group>
                    </group>
                    <group>
                        <group name="limits" string="Limits &amp; Features">
                            <field name="max_users"/>
                            <field name="max_storage_gb"/>
                        </group>
                        <group name="features_group" string="Features">
                            <field name="features" nolabel="1"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- SaaS Plan Search View -->
    <record id="view_saas_plan_search" model="ir.ui.view">
        <field name="name">saas.plan.search</field>
        <field name="model">saas.plan</field>
        <field name="type">search</field>
        <field name="arch" type="xml">
            <search string="Search SaaS Plans">
                <field name="name"/>
                <field name="description"/>
                <filter name="active" string="Active" domain="[('active', '=', True)]"/>
                <filter name="archived" string="Archived" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter name="group_by_active" string="Status" domain="[]" context="{'group_by': 'active'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- SaaS Plan Action -->
    <record id="action_saas_plan" model="ir.actions.act_window">
        <field name="name">SaaS Plans</field>
        <field name="res_model">saas.plan</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_saas_plan_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first SaaS plan!
            </p>
            <p>
                Define subscription plans with pricing and features for your SaaS offering.
            </p>
        </field>
    </record>
</odoo>
