from odoo import models, fields, api
from datetime import datetime, timedelta

class SaasClient(models.Model):
    _name = 'saas.client'
    _description = 'SaaS Client'
    _order = 'name'
    
    # Basic Information
    name = fields.Char(
        string='Client Name', 
        required=True,
        help='Name of the SaaS client'
    )
    
    email = fields.Char(
        string='Email',
        required=True,
        help='Primary contact email for the client'
    )
    
    phone = fields.Char(
        string='Phone',
        help='Contact phone number'
    )
    
    # Status and State
    state = fields.Selection([
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('cancelled', 'Cancelled'),
    ], string='Status', default='draft', required=True)
    
    # Dates
    created_date = fields.Datetime(
        string='Created Date',
        default=fields.Datetime.now,
        readonly=True
    )
    
    # Company Information
    company_name = fields.Char(
        string='Company Name',
        help='Client company name'
    )
    
    # Relationships - temporarily commented to debug circular dependency
    # instance_ids = fields.One2many(
    #     'saas.instance',
    #     'client_id',
    #     string='Instances',
    #     help='SaaS instances owned by this client'
    # )

    # Basic computed fields
    display_name = fields.Char(
        string='Display Name',
        compute='_compute_display_name',
        store=True
    )

    # instance_count = fields.Integer(
    #     string='Instance Count',
    #     compute='_compute_instance_count',
    #     help='Number of instances for this client'
    # )
    
    @api.depends('name', 'company_name')
    def _compute_display_name(self):
        for record in self:
            if record.company_name:
                record.display_name = f"{record.name} ({record.company_name})"
            else:
                record.display_name = record.name

    # @api.depends('instance_ids')
    # def _compute_instance_count(self):
    #     for record in self:
    #         record.instance_count = len(record.instance_ids)
    
    # Action methods
    def action_activate(self):
        """Activate the client"""
        self.write({'state': 'active'})
        return True
    
    def action_suspend(self):
        """Suspend the client"""
        self.write({'state': 'suspended'})
        return True
    
    def action_cancel(self):
        """Cancel the client"""
        self.write({'state': 'cancelled'})
        return True
