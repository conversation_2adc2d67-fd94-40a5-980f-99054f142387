/** @odoo-module **/

import { Component, useState, onMounted } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";

/**
 * SaaS Dashboard Component
 * Main dashboard for SaaS management backend
 */
class SaasDashboardComponent extends Component {
    setup() {
        this.rpc = useService("rpc");
        this.notification = useService("notification");
        this.action = useService("action");
        
        this.state = useState({
            stats: {
                total_revenue: 0,
                total_instances: 0,
                total_clients: 0,
                total_servers: 0,
                revenue_change: 0,
                instances_change: 0,
                clients_change: 0,
                servers_change: 0
            },
            recentActivity: [],
            chartData: {
                revenue: [],
                instances: [],
                labels: []
            },
            loading: true,
            refreshing: false
        });

        onMounted(async () => {
            await this.loadDashboardData();
            this.initializeCharts();
        });
    }

    /**
     * Load dashboard data from backend
     */
    async loadDashboardData() {
        try {
            this.state.loading = true;
            
            const [stats, activity, chartData] = await Promise.all([
                this.loadStats(),
                this.loadRecentActivity(),
                this.loadChartData()
            ]);
            
            this.state.stats = stats;
            this.state.recentActivity = activity;
            this.state.chartData = chartData;
            
        } catch (error) {
            console.error("Error loading dashboard data:", error);
            this.loadDemoData();
        } finally {
            this.state.loading = false;
            this.updateDashboardDisplay();
        }
    }

    /**
     * Load statistics from backend
     */
    async loadStats() {
        try {
            return await this.rpc("/saas/api/dashboard/stats", {});
        } catch (error) {
            console.error("Error loading stats:", error);
            return this.getDemoStats();
        }
    }

    /**
     * Load recent activity from backend
     */
    async loadRecentActivity() {
        try {
            return await this.rpc("/saas/api/dashboard/activity", {});
        } catch (error) {
            console.error("Error loading activity:", error);
            return this.getDemoActivity();
        }
    }

    /**
     * Load chart data from backend
     */
    async loadChartData() {
        try {
            return await this.rpc("/saas/api/dashboard/charts", {});
        } catch (error) {
            console.error("Error loading chart data:", error);
            return this.getDemoChartData();
        }
    }

    /**
     * Load demo data when backend is not available
     */
    loadDemoData() {
        this.state.stats = this.getDemoStats();
        this.state.recentActivity = this.getDemoActivity();
        this.state.chartData = this.getDemoChartData();
    }

    /**
     * Get demo statistics
     */
    getDemoStats() {
        return {
            total_revenue: 15420,
            total_instances: 47,
            total_clients: 23,
            total_servers: 3,
            revenue_change: 12.5,
            instances_change: 8.3,
            clients_change: 15.2,
            servers_change: 0
        };
    }

    /**
     * Get demo activity
     */
    getDemoActivity() {
        return [
            {
                id: 1,
                type: "instance",
                icon: "🚀",
                text: "New instance created for Acme Corp",
                time: "2 minutes ago"
            },
            {
                id: 2,
                type: "payment",
                icon: "💳",
                text: "Payment received from TechStart Ltd",
                time: "15 minutes ago"
            },
            {
                id: 3,
                type: "client",
                icon: "👤",
                text: "New client registered: Digital Solutions",
                time: "1 hour ago"
            },
            {
                id: 4,
                type: "backup",
                icon: "💾",
                text: "Backup completed for all instances",
                time: "2 hours ago"
            },
            {
                id: 5,
                type: "instance",
                icon: "⚙️",
                text: "Instance upgraded for Global Retail",
                time: "3 hours ago"
            }
        ];
    }

    /**
     * Get demo chart data
     */
    getDemoChartData() {
        return {
            revenue: [8500, 9200, 10100, 11300, 12800, 14200, 15420],
            instances: [25, 28, 32, 37, 41, 45, 47],
            labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul"]
        };
    }

    /**
     * Update dashboard display
     */
    updateDashboardDisplay() {
        this.updateStatsCards();
        this.updateActivityList();
        this.updateCharts();
    }

    /**
     * Update statistics cards
     */
    updateStatsCards() {
        const statsElements = {
            'total-revenue': this.formatCurrency(this.state.stats.total_revenue),
            'total-instances': this.state.stats.total_instances,
            'total-clients': this.state.stats.total_clients,
            'total-servers': this.state.stats.total_servers,
            'revenue-change': this.formatChange(this.state.stats.revenue_change),
            'instances-change': this.formatChange(this.state.stats.instances_change),
            'clients-change': this.formatChange(this.state.stats.clients_change),
            'servers-change': this.formatChange(this.state.stats.servers_change)
        };

        Object.entries(statsElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });

        // Update change indicators
        this.updateChangeIndicators();
    }

    /**
     * Update change indicators
     */
    updateChangeIndicators() {
        const changes = {
            'revenue-change': this.state.stats.revenue_change,
            'instances-change': this.state.stats.instances_change,
            'clients-change': this.state.stats.clients_change,
            'servers-change': this.state.stats.servers_change
        };

        Object.entries(changes).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.className = `saas-stat-change ${value >= 0 ? 'positive' : 'negative'}`;
            }
        });
    }

    /**
     * Update activity list
     */
    updateActivityList() {
        const container = document.querySelector('.saas-activity-list');
        if (!container) return;

        container.innerHTML = this.state.recentActivity.map(activity => `
            <li class="saas-activity-item">
                <div class="saas-activity-icon ${activity.type}">
                    ${activity.icon}
                </div>
                <div class="saas-activity-content">
                    <div class="saas-activity-text">${activity.text}</div>
                    <div class="saas-activity-time">${activity.time}</div>
                </div>
            </li>
        `).join('');
    }

    /**
     * Initialize charts
     */
    initializeCharts() {
        // Initialize revenue chart
        this.initializeRevenueChart();
        
        // Initialize instances chart
        this.initializeInstancesChart();
    }

    /**
     * Initialize revenue chart
     */
    initializeRevenueChart() {
        const canvas = document.getElementById('revenue-chart');
        if (!canvas) return;

        // Simple canvas-based chart implementation
        const ctx = canvas.getContext('2d');
        const data = this.state.chartData.revenue;
        const labels = this.state.chartData.labels;
        
        this.drawLineChart(ctx, data, labels, '#28a745');
    }

    /**
     * Initialize instances chart
     */
    initializeInstancesChart() {
        const canvas = document.getElementById('instances-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const data = this.state.chartData.instances;
        const labels = this.state.chartData.labels;
        
        this.drawLineChart(ctx, data, labels, '#17a2b8');
    }

    /**
     * Draw simple line chart
     */
    drawLineChart(ctx, data, labels, color) {
        const canvas = ctx.canvas;
        const width = canvas.width;
        const height = canvas.height;
        const padding = 40;
        
        // Clear canvas
        ctx.clearRect(0, 0, width, height);
        
        // Calculate scales
        const maxValue = Math.max(...data);
        const minValue = Math.min(...data);
        const valueRange = maxValue - minValue || 1;
        
        const xStep = (width - 2 * padding) / (data.length - 1);
        const yScale = (height - 2 * padding) / valueRange;
        
        // Draw grid lines
        ctx.strokeStyle = '#e9ecef';
        ctx.lineWidth = 1;
        
        // Vertical grid lines
        for (let i = 0; i < data.length; i++) {
            const x = padding + i * xStep;
            ctx.beginPath();
            ctx.moveTo(x, padding);
            ctx.lineTo(x, height - padding);
            ctx.stroke();
        }
        
        // Horizontal grid lines
        for (let i = 0; i <= 5; i++) {
            const y = padding + (i * (height - 2 * padding)) / 5;
            ctx.beginPath();
            ctx.moveTo(padding, y);
            ctx.lineTo(width - padding, y);
            ctx.stroke();
        }
        
        // Draw line
        ctx.strokeStyle = color;
        ctx.lineWidth = 3;
        ctx.beginPath();
        
        for (let i = 0; i < data.length; i++) {
            const x = padding + i * xStep;
            const y = height - padding - (data[i] - minValue) * yScale;
            
            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        }
        
        ctx.stroke();
        
        // Draw points
        ctx.fillStyle = color;
        for (let i = 0; i < data.length; i++) {
            const x = padding + i * xStep;
            const y = height - padding - (data[i] - minValue) * yScale;
            
            ctx.beginPath();
            ctx.arc(x, y, 4, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        // Draw labels
        ctx.fillStyle = '#666';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        
        for (let i = 0; i < labels.length; i++) {
            const x = padding + i * xStep;
            ctx.fillText(labels[i], x, height - 10);
        }
    }

    /**
     * Update charts
     */
    updateCharts() {
        this.initializeCharts();
    }

    /**
     * Format currency
     */
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0
        }).format(amount);
    }

    /**
     * Format change percentage
     */
    formatChange(change) {
        const sign = change >= 0 ? '+' : '';
        return `${sign}${change.toFixed(1)}%`;
    }

    /**
     * Refresh dashboard data
     */
    async refreshDashboard() {
        this.state.refreshing = true;
        await this.loadDashboardData();
        this.state.refreshing = false;
        this.notification.add("Dashboard refreshed", { type: "success" });
    }

    /**
     * Navigate to instances
     */
    navigateToInstances() {
        this.action.doAction({
            type: 'ir.actions.act_window',
            res_model: 'saas.instance',
            view_mode: 'list,form',
            views: [[false, 'list'], [false, 'form']],
            target: 'current',
        });
    }

    /**
     * Navigate to clients
     */
    navigateToClients() {
        this.action.doAction({
            type: 'ir.actions.act_window',
            res_model: 'saas.client',
            view_mode: 'list,form',
            views: [[false, 'list'], [false, 'form']],
            target: 'current',
        });
    }

    /**
     * Navigate to servers
     */
    navigateToServers() {
        this.action.doAction({
            type: 'ir.actions.act_window',
            res_model: 'saas.server',
            view_mode: 'list,form',
            views: [[false, 'list'], [false, 'form']],
            target: 'current',
        });
    }
}

/**
 * SaaS Dashboard Application
 * Main application class for dashboard functionality
 */
class SaasDashboardApp {
    constructor() {
        this.component = null;
        this.init();
    }

    async init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeApp());
        } else {
            this.initializeApp();
        }
    }

    initializeApp() {
        // Initialize dashboard component if dashboard section exists
        const dashboardSection = document.querySelector('.saas-dashboard-container');
        if (dashboardSection) {
            this.component = new SaasDashboardComponent();
            this.attachEventListeners();
        }
    }

    attachEventListeners() {
        // Refresh button
        const refreshBtn = document.querySelector('.saas-refresh-dashboard-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshDashboard());
        }

        // Navigation buttons
        const navButtons = {
            '.saas-nav-instances': () => this.navigateToInstances(),
            '.saas-nav-clients': () => this.navigateToClients(),
            '.saas-nav-servers': () => this.navigateToServers()
        };

        Object.entries(navButtons).forEach(([selector, handler]) => {
            const button = document.querySelector(selector);
            if (button) {
                button.addEventListener('click', handler);
            }
        });
    }

    async refreshDashboard() {
        if (this.component) {
            await this.component.refreshDashboard();
        }
    }

    navigateToInstances() {
        if (this.component) {
            this.component.navigateToInstances();
        }
    }

    navigateToClients() {
        if (this.component) {
            this.component.navigateToClients();
        }
    }

    navigateToServers() {
        if (this.component) {
            this.component.navigateToServers();
        }
    }
}

// Initialize the application
const saasDashboard = new SaasDashboardApp();

// Export for global access
window.saasDashboard = saasDashboard;

// Register component for Odoo
registry.category("web_components").add("saas_dashboard", SaasDashboardComponent);
