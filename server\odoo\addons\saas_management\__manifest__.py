# -*- coding: utf-8 -*-
{
    'name': 'SaaS Management Platform',
    'version': '********.0',
    'category': 'SaaS/Management',
    'summary': 'Complete SaaS platform for managing Odoo instances, subscriptions, and billing',
    'description': """
SaaS Management Platform
========================

A comprehensive SaaS platform that provides:

Core Features:
--------------
* **Automated Instance Creation**: Deploy new Odoo instances with one click
* **Multi-version Support**: Support for all Odoo versions (8.0 to 18.0+)
* **Subscription Management**: Automated billing and renewal processes
* **Customer Portal**: Self-service portal for customers
* **Domain & SSL Management**: Automatic SSL certificate generation
* **Backup & Restore**: Automated backup and restore functionality
* **Resource Management**: CPU and RAM allocation per instance
* **Trial System**: Free trial instances with automatic conversion

Advanced Features:
------------------
* **Multi-server Deployment**: Deploy across multiple servers
* **Git Integration**: Connect custom repositories to instances
* **Pricing Plans**: Flexible pricing based on users and apps
* **Payment Integration**: Multiple payment gateway support
* **Dashboard & Analytics**: Comprehensive reporting and analytics
* **Email Automation**: Automated notifications and reminders

Technical Features:
-------------------
* **Odoo 18 Compatible**: Built for Odoo 18 with best practices
* **Security**: Role-based access control and data isolation
* **API Integration**: RESTful APIs for external integrations
* **Scalable Architecture**: Designed for high-volume deployments
    """,
    'author': 'SaaS Management Team',
    'website': 'https://www.saas-management.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'mail',
        'website',
        'sale_management',
        'account',
        'payment',
        'portal',
        'website_sale',
        'contacts',
    ],
    'data': [
        # Security
        # 'security/security.xml',
        # 'security/ir.model.access.csv',

        # Data
        # 'data/saas_data.xml',
        # 'data/email_templates.xml',
        # 'data/cron_jobs.xml',

        # Views
        'views/saas_views_minimal.xml',
        'views/saas_menu.xml',
    ],
    'demo': [
        'demo/saas_demo.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'saas_management/static/src/css/saas_backend.css',
            'saas_management/static/src/js/saas_dashboard.js',
            'saas_management/static/src/js/saas_instance_manager.js',
        ],
        'web.assets_frontend': [
            'saas_management/static/src/css/saas_frontend.css',
            'saas_management/static/src/js/saas_pricing.js',
            'saas_management/static/src/js/saas_portal.js',
        ],
        'website.assets_editor': [
            'saas_management/static/src/css/saas_editor.css',
        ],
    },
    'images': [
        'static/description/banner.png',
        'static/description/icon.png',
    ],
    'installable': True,
    'auto_install': False,
    'application': True,
    'sequence': 10,
    # 'external_dependencies': {
    #     'python': [
    #         'docker',
    #         'paramiko',
    #         'psycopg2',
    #         'requests',
    #         'cryptography',
    #     ],
    # },
}
