#!/usr/bin/env python3
import sys
import os

# Add Odoo to path
sys.path.append('c:/Program Files/Odoo 18.0.20250519/server')

import odoo
from odoo import api, SUPERUSER_ID

def test_saas_upgrade():
    """Test saas_platform module upgrade after fixing external ID reference"""
    
    # Configure Odoo
    odoo.tools.config.parse_config(['-d', 'ecomplus'])
    
    with odoo.registry('ecomplus').cursor() as cr:
        env = api.Environment(cr, SUPERUSER_ID, {})
        
        # Test upgrade
        module = env['ir.module.module'].search([('name', '=', 'saas_platform')])
        print(f'Current module state: {module.state}')
        
        try:
            print('Testing upgrade after fixing external ID reference...')
            module.button_immediate_upgrade()
            print('✅ Upgrade completed successfully!')
            
            # Test models
            models_to_test = ['saas.client', 'saas.plan', 'saas.instance']
            working_models = []
            
            for model_name in models_to_test:
                try:
                    model = env[model_name]
                    print(f'✅ {model_name} model exists with {len(model._fields)} fields')
                    working_models.append(model_name)
                except Exception as e:
                    print(f'❌ {model_name} error: {e}')
            
            # Test saas.instance specifically
            if 'saas.instance' in working_models:
                print('\n🧪 Testing saas.instance functionality...')
                
                # Create test data
                client = env['saas.client'].create({
                    'name': 'Test Client',
                    'email': '<EMAIL>'
                })
                
                plan = env['saas.plan'].create({
                    'name': 'Test Plan',
                    'monthly_price': 50.0,
                    'max_users': 10,
                    'max_storage_gb': 20.0
                })
                
                instance = env['saas.instance'].create({
                    'name': 'Test Instance',
                    'subdomain': 'test123',
                    'client_id': client.id,
                    'plan_id': plan.id,
                    'current_users': 3,
                    'storage_used_gb': 5.5
                })
                
                print(f'✅ Instance: {instance.name}')
                print(f'✅ URL: {instance.url}')
                print(f'✅ Usage: {instance.usage_percentage:.1f}%')
                print(f'✅ Status: {instance.status}')
                
                # Test workflow
                instance.action_provision()
                print(f'✅ Provisioned: {instance.status}')
                
                instance.action_activate()
                print(f'✅ Activated: {instance.status}')
                
                # Clean up
                instance.unlink()
                plan.unlink()
                client.unlink()
                print('✅ Test data cleaned up')
                
                print('\n🎉 saas.instance model working perfectly!')
            
            print(f'\n🎉 Working models: {working_models}')
            return True
            
        except Exception as e:
            print(f'❌ Error: {e}')
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    success = test_saas_upgrade()
    sys.exit(0 if success else 1)
