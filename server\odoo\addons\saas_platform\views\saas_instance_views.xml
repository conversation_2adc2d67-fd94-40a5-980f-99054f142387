<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- SaaS Instance List View -->
    <record id="view_saas_instance_list" model="ir.ui.view">
        <field name="name">saas.instance.list</field>
        <field name="model">saas.instance</field>
        <field name="type">list</field>
        <field name="arch" type="xml">
            <list string="SaaS Instances">
                <field name="name"/>
                <field name="client_id"/>
                <field name="plan_id"/>
                <field name="subdomain"/>
                <field name="status"/>
                <field name="current_users"/>
                <field name="storage_used_gb"/>
                <field name="expiry_date"/>
                <field name="is_over_limit" widget="boolean"/>
            </list>
        </field>
    </record>

    <!-- SaaS Instance Form View -->
    <record id="view_saas_instance_form" model="ir.ui.view">
        <field name="name">saas.instance.form</field>
        <field name="model">saas.instance</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form string="SaaS Instance">
                <header>
                    <button name="action_provision" string="Provision" type="object" 
                            class="btn-primary" invisible="status != 'draft'"/>
                    <button name="action_activate" string="Activate" type="object" 
                            class="btn-success" invisible="status != 'provisioning'"/>
                    <button name="action_suspend" string="Suspend" type="object" 
                            class="btn-warning" invisible="status not in ['active']"/>
                    <button name="action_terminate" string="Terminate" type="object" 
                            class="btn-danger" invisible="status in ['terminated']"/>
                    <field name="status" widget="statusbar" 
                           statusbar_visible="draft,provisioning,active,suspended,expired,terminated"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="%(action_saas_instance)d" type="action" 
                                class="oe_stat_button" icon="fa-external-link"
                                invisible="not url">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Open Instance</span>
                            </div>
                        </button>
                    </div>
                    
                    <group>
                        <group name="basic_info" string="Basic Information">
                            <field name="name"/>
                            <field name="client_id"/>
                            <field name="plan_id"/>
                            <field name="subdomain"/>
                            <field name="url" widget="url" readonly="1"/>
                        </group>
                        <group name="dates" string="Dates">
                            <field name="created_date" readonly="1"/>
                            <field name="activated_date" readonly="1"/>
                            <field name="expiry_date"/>
                            <field name="days_until_expiry" readonly="1"/>
                        </group>
                    </group>
                    
                    <group>
                        <group name="technical" string="Technical Information">
                            <field name="database_name" readonly="1"/>
                        </group>
                        <group name="usage" string="Usage Statistics">
                            <field name="current_users"/>
                            <field name="storage_used_gb"/>
                            <field name="usage_percentage" readonly="1" widget="percentage"/>
                            <field name="is_over_limit" readonly="1"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- SaaS Instance Search View -->
    <record id="view_saas_instance_search" model="ir.ui.view">
        <field name="name">saas.instance.search</field>
        <field name="model">saas.instance</field>
        <field name="type">search</field>
        <field name="arch" type="xml">
            <search string="Search SaaS Instances">
                <field name="name"/>
                <field name="subdomain"/>
                <field name="client_id"/>
                <field name="plan_id"/>
                <filter name="active" string="Active" domain="[('status', '=', 'active')]"/>
                <filter name="suspended" string="Suspended" domain="[('status', '=', 'suspended')]"/>
                <filter name="over_limit" string="Over Limit" domain="[('is_over_limit', '=', True)]"/>
                <filter name="expiring_soon" string="Expiring Soon" 
                        domain="[('days_until_expiry', '&lt;=', 30), ('days_until_expiry', '&gt;', 0)]"/>
                <group expand="0" string="Group By">
                    <filter name="group_by_status" string="Status" domain="[]" context="{'group_by': 'status'}"/>
                    <filter name="group_by_client" string="Client" domain="[]" context="{'group_by': 'client_id'}"/>
                    <filter name="group_by_plan" string="Plan" domain="[]" context="{'group_by': 'plan_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- SaaS Instance Action -->
    <record id="action_saas_instance" model="ir.actions.act_window">
        <field name="name">SaaS Instances</field>
        <field name="res_model">saas.instance</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_saas_instance_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first SaaS instance!
            </p>
            <p>
                Manage SaaS instances for your clients with their subscription plans.
            </p>
        </field>
    </record>
</odoo>
