# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
import logging

_logger = logging.getLogger(__name__)


class SaasServer(models.Model):
    _name = 'saas.server'
    _description = 'SaaS Server'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'sequence, name'

    name = fields.Char(
        string='Server Name',
        required=True,
        tracking=True,
        help='Name of the server'
    )
    
    code = fields.Char(
        string='Server Code',
        required=True,
        help='Unique code for the server'
    )
    
    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help='Sequence for ordering servers'
    )
    
    active = fields.Boolean(
        string='Active',
        default=True,
        tracking=True
    )
    
    # Server Configuration
    host = fields.Char(
        string='Host/IP Address',
        required=True,
        help='Server hostname or IP address'
    )
    
    port = fields.Integer(
        string='SSH Port',
        default=22,
        help='SSH port for server access'
    )
    
    username = fields.Char(
        string='Username',
        required=True,
        help='SSH username for server access'
    )
    
    password = fields.Char(
        string='Password',
        help='SSH password (if not using key authentication)'
    )
    
    private_key = fields.Text(
        string='Private Key',
        help='SSH private key for authentication'
    )
    
    # Server Type
    server_type = fields.Selection([
        ('master', 'Master Server'),
        ('instance', 'Instance Server'),
        ('database', 'Database Server'),
        ('storage', 'Storage Server'),
    ], string='Server Type', default='instance', required=True)
    
    # Capacity and Resources
    max_instances = fields.Integer(
        string='Max Instances',
        default=100,
        help='Maximum number of instances this server can handle'
    )
    
    current_instances = fields.Integer(
        string='Current Instances',
        compute='_compute_current_instances',
        store=True,
        help='Number of instances currently running on this server'
    )
    
    cpu_cores = fields.Integer(
        string='CPU Cores',
        default=4,
        help='Number of CPU cores available'
    )
    
    memory_gb = fields.Integer(
        string='Memory (GB)',
        default=8,
        help='Total memory in GB'
    )
    
    storage_gb = fields.Integer(
        string='Storage (GB)',
        default=100,
        help='Total storage in GB'
    )
    
    # Usage Statistics
    cpu_usage_percent = fields.Float(
        string='CPU Usage (%)',
        help='Current CPU usage percentage'
    )
    
    memory_usage_percent = fields.Float(
        string='Memory Usage (%)',
        help='Current memory usage percentage'
    )
    
    storage_usage_percent = fields.Float(
        string='Storage Usage (%)',
        help='Current storage usage percentage'
    )
    
    # Network Configuration
    public_ip = fields.Char(
        string='Public IP',
        help='Public IP address of the server'
    )
    
    private_ip = fields.Char(
        string='Private IP',
        help='Private IP address of the server'
    )
    
    # Docker Configuration
    docker_enabled = fields.Boolean(
        string='Docker Enabled',
        default=True,
        help='Whether Docker is enabled on this server'
    )
    
    docker_registry = fields.Char(
        string='Docker Registry',
        help='Docker registry URL for pulling images'
    )
    
    # Database Configuration
    postgres_host = fields.Char(
        string='PostgreSQL Host',
        help='PostgreSQL server host'
    )
    
    postgres_port = fields.Integer(
        string='PostgreSQL Port',
        default=5432,
        help='PostgreSQL server port'
    )
    
    postgres_user = fields.Char(
        string='PostgreSQL User',
        help='PostgreSQL username'
    )
    
    postgres_password = fields.Char(
        string='PostgreSQL Password',
        help='PostgreSQL password'
    )
    
    # Backup Configuration
    backup_enabled = fields.Boolean(
        string='Backup Enabled',
        default=True,
        help='Whether backup is enabled on this server'
    )
    
    backup_path = fields.Char(
        string='Backup Path',
        default='/opt/saas/backups',
        help='Path where backups are stored'
    )
    
    # Monitoring
    monitoring_enabled = fields.Boolean(
        string='Monitoring Enabled',
        default=True,
        help='Whether monitoring is enabled'
    )
    
    last_health_check = fields.Datetime(
        string='Last Health Check',
        help='Last time server health was checked'
    )
    
    health_status = fields.Selection([
        ('healthy', 'Healthy'),
        ('warning', 'Warning'),
        ('critical', 'Critical'),
        ('offline', 'Offline'),
    ], string='Health Status', default='healthy')
    
    # Instances
    instance_ids = fields.One2many(
        'saas.instance',
        'server_id',
        string='Instances',
        help='Instances running on this server'
    )
    
    # Domains
    domain_ids = fields.One2many(
        'saas.domain',
        'server_id',
        string='Domains',
        help='Domains configured on this server'
    )
    
    # Notes
    notes = fields.Text(
        string='Notes',
        help='Additional notes about the server'
    )

    @api.depends('instance_ids', 'instance_ids.state')
    def _compute_current_instances(self):
        for server in self:
            server.current_instances = len(
                server.instance_ids.filtered(lambda i: i.state in ['running', 'suspended'])
            )

    @api.constrains('code')
    def _check_code_unique(self):
        for server in self:
            if server.code:
                existing = self.search([
                    ('code', '=', server.code),
                    ('id', '!=', server.id)
                ])
                if existing:
                    raise ValidationError(_('Server code must be unique.'))

    @api.constrains('max_instances', 'cpu_cores', 'memory_gb', 'storage_gb')
    def _check_positive_values(self):
        for server in self:
            if server.max_instances <= 0:
                raise ValidationError(_('Max instances must be positive.'))
            if server.cpu_cores <= 0:
                raise ValidationError(_('CPU cores must be positive.'))
            if server.memory_gb <= 0:
                raise ValidationError(_('Memory must be positive.'))
            if server.storage_gb <= 0:
                raise ValidationError(_('Storage must be positive.'))

    def test_connection(self):
        """Test SSH connection to the server"""
        self.ensure_one()
        
        try:
            # Test SSH connection
            result = self._execute_ssh_command('echo "Connection test successful"')
            if result:
                self.message_post(body=_('SSH connection test successful'))
                return True
            else:
                raise UserError(_('SSH connection test failed'))
        except Exception as e:
            _logger.error(f"SSH connection test failed for server {self.name}: {str(e)}")
            raise UserError(_('SSH connection test failed: %s') % str(e))

    def _execute_ssh_command(self, command):
        """Execute a command on the server via SSH"""
        try:
            import paramiko
            
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # Connect using password or key
            if self.private_key:
                # Use private key authentication
                key = paramiko.RSAKey.from_private_key_file(self.private_key)
                ssh.connect(
                    hostname=self.host,
                    port=self.port,
                    username=self.username,
                    pkey=key
                )
            else:
                # Use password authentication
                ssh.connect(
                    hostname=self.host,
                    port=self.port,
                    username=self.username,
                    password=self.password
                )
            
            # Execute command
            stdin, stdout, stderr = ssh.exec_command(command)
            result = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            
            ssh.close()
            
            if error:
                _logger.error(f"SSH command error: {error}")
                return False
            
            return result
            
        except Exception as e:
            _logger.error(f"SSH execution failed: {str(e)}")
            return False

    def check_health(self):
        """Check server health status"""
        self.ensure_one()
        
        try:
            # Check if server is reachable
            result = self._execute_ssh_command('uptime')
            if not result:
                self.health_status = 'offline'
                return
            
            # Check CPU usage
            cpu_result = self._execute_ssh_command("top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1")
            if cpu_result:
                try:
                    self.cpu_usage_percent = float(cpu_result.strip())
                except ValueError:
                    pass
            
            # Check memory usage
            mem_result = self._execute_ssh_command("free | grep Mem | awk '{printf \"%.2f\", $3/$2 * 100.0}'")
            if mem_result:
                try:
                    self.memory_usage_percent = float(mem_result.strip())
                except ValueError:
                    pass
            
            # Check disk usage
            disk_result = self._execute_ssh_command("df -h / | awk 'NR==2{print $5}' | cut -d'%' -f1")
            if disk_result:
                try:
                    self.storage_usage_percent = float(disk_result.strip())
                except ValueError:
                    pass
            
            # Determine health status
            if self.cpu_usage_percent > 90 or self.memory_usage_percent > 90 or self.storage_usage_percent > 90:
                self.health_status = 'critical'
            elif self.cpu_usage_percent > 80 or self.memory_usage_percent > 80 or self.storage_usage_percent > 80:
                self.health_status = 'warning'
            else:
                self.health_status = 'healthy'
            
            self.last_health_check = fields.Datetime.now()
            
        except Exception as e:
            _logger.error(f"Health check failed for server {self.name}: {str(e)}")
            self.health_status = 'offline'

    def action_view_instances(self):
        """View instances on this server"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Instances'),
            'res_model': 'saas.instance',
            'view_mode': 'list,form',
            'domain': [('server_id', '=', self.id)],
            'context': {'default_server_id': self.id},
        }

    def get_available_capacity(self):
        """Get available capacity on this server"""
        self.ensure_one()
        return {
            'instances': self.max_instances - self.current_instances,
            'cpu': self.cpu_cores * (1 - self.cpu_usage_percent / 100),
            'memory': self.memory_gb * (1 - self.memory_usage_percent / 100),
            'storage': self.storage_gb * (1 - self.storage_usage_percent / 100),
        }

    def can_host_instance(self, plan):
        """Check if server can host an instance with given plan"""
        self.ensure_one()
        
        if self.current_instances >= self.max_instances:
            return False
        
        capacity = self.get_available_capacity()
        
        # Check if server has enough resources
        if plan.cpu_limit > capacity['cpu']:
            return False
        
        if plan.memory_limit_mb / 1024 > capacity['memory']:
            return False
        
        return True
