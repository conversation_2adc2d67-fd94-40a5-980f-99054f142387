<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Cleanup Expired Backups -->
        <record id="cron_cleanup_expired_backups" model="ir.cron">
            <field name="name">SaaS: Cleanup Expired Backups</field>
            <field name="model_id" ref="model_saas_backup"/>
            <field name="state">code</field>
            <field name="code">model.cleanup_expired_backups()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- Check Server Health -->
        <record id="cron_check_server_health" model="ir.cron">
            <field name="name">SaaS: Check Server Health</field>
            <field name="model_id" ref="model_saas_server"/>
            <field name="state">code</field>
            <field name="code">
servers = model.search([('active', '=', True)])
for server in servers:
    server.check_health()
            </field>
            <field name="interval_number">15</field>
            <field name="interval_type">minutes</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- Check Trial Expiration -->
        <record id="cron_check_trial_expiration" model="ir.cron">
            <field name="name">SaaS: Check Trial Expiration</field>
            <field name="model_id" ref="model_saas_instance"/>
            <field name="state">code</field>
            <field name="code">
from datetime import datetime, timedelta

# Find trials expiring in 3 days
expiring_soon = model.search([
    ('instance_type', '=', 'trial'),
    ('state', '=', 'running'),
    ('trial_end_date', '&lt;=', datetime.now() + timedelta(days=3)),
    ('trial_end_date', '&gt;', datetime.now())
])

# Send expiration warnings
template = env.ref('saas_management.email_template_trial_expiring', raise_if_not_found=False)
if template:
    for instance in expiring_soon:
        template.send_mail(instance.id, force_send=True)

# Find expired trials
expired_trials = model.search([
    ('instance_type', '=', 'trial'),
    ('state', '=', 'running'),
    ('trial_end_date', '&lt;', datetime.now())
])

# Suspend expired trials
for instance in expired_trials:
    instance.action_suspend()
    instance.message_post(body='Trial expired - instance suspended')
            </field>
            <field name="interval_number">6</field>
            <field name="interval_type">hours</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- Auto Backup Instances -->
        <record id="cron_auto_backup_instances" model="ir.cron">
            <field name="name">SaaS: Auto Backup Instances</field>
            <field name="model_id" ref="model_saas_instance"/>
            <field name="state">code</field>
            <field name="code">
from datetime import datetime, timedelta

# Find instances that need backup (running instances with backup enabled plans)
instances_to_backup = model.search([
    ('state', '=', 'running'),
    ('plan_id.backup_enabled', '=', True),
    '|',
    ('last_backup_date', '=', False),
    ('last_backup_date', '&lt;', datetime.now() - timedelta(days=1))
])

# Create backups
for instance in instances_to_backup:
    try:
        backup_vals = {
            'name': f'Auto Backup {instance.name} - {datetime.now().strftime("%Y-%m-%d %H:%M")}',
            'instance_id': instance.id,
            'backup_type': 'scheduled',
            'state': 'draft',
        }
        backup = env['saas.backup'].create(backup_vals)
        backup.action_create_backup()
    except Exception as e:
        # Log error but continue with other instances
        import logging
        _logger = logging.getLogger(__name__)
        _logger.error(f'Auto backup failed for instance {instance.name}: {str(e)}')
            </field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

        <!-- Process Subscription Renewals -->
        <record id="cron_process_subscription_renewals" model="ir.cron">
            <field name="name">SaaS: Process Subscription Renewals</field>
            <field name="model_id" ref="model_saas_subscription"/>
            <field name="state">code</field>
            <field name="code">
from datetime import datetime, timedelta

# Find subscriptions due for renewal
due_renewals = model.search([
    ('state', '=', 'active'),
    ('auto_renew', '=', True),
    ('next_billing_date', '&lt;=', datetime.now().date())
])

# Process renewals
for subscription in due_renewals:
    try:
        subscription.action_renew()
        subscription.message_post(body='Subscription automatically renewed')
    except Exception as e:
        # Log error and mark subscription for manual review
        import logging
        _logger = logging.getLogger(__name__)
        _logger.error(f'Auto renewal failed for subscription {subscription.name}: {str(e)}')
        subscription.message_post(body=f'Auto renewal failed: {str(e)}')
            </field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="active">True</field>
            <field name="user_id" ref="base.user_root"/>
        </record>

    </data>
</odoo>
