# -*- coding: utf-8 -*-

from odoo import models, fields, api


class SaasPlan(models.Model):
    _name = 'saas.plan'
    _description = 'SaaS Service Plan'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'sequence, name'

    name = fields.Char(
        string='Plan Name',
        required=True,
        tracking=True,
        help='Name of the service plan'
    )
    
    code = fields.Char(
        string='Plan Code',
        required=True,
        tracking=True,
        help='Unique code for the plan'
    )
    
    description = fields.Text(
        string='Description',
        help='Detailed description of the plan'
    )
    
    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help='Sequence for ordering plans'
    )
    
    state = fields.Selection([
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('deprecated', 'Deprecated'),
        ('archived', 'Archived'),
    ], string='Status', default='draft', required=True, tracking=True)
    
    # Pricing
    price = fields.Float(
        string='Price',
        digits='Product Price',
        tracking=True,
        help='Monthly price for this plan'
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id,
        required=True
    )
    
    billing_cycle = fields.Selection([
        ('monthly', 'Monthly'),
        ('quarterly', 'Quarterly'),
        ('yearly', 'Yearly'),
    ], string='Billing Cycle', default='monthly', required=True)
    
    # Plan Limits
    max_users = fields.Integer(
        string='Max Users',
        default=1,
        help='Maximum number of users allowed'
    )
    
    max_storage_gb = fields.Float(
        string='Max Storage (GB)',
        default=1.0,
        help='Maximum storage in GB'
    )
    
    max_databases = fields.Integer(
        string='Max Databases',
        default=1,
        help='Maximum number of databases allowed'
    )
    
    # Features
    features = fields.Text(
        string='Features',
        help='List of features included in this plan'
    )
    
    has_custom_domain = fields.Boolean(
        string='Custom Domain',
        default=False,
        help='Allow custom domain'
    )
    
    has_ssl = fields.Boolean(
        string='SSL Certificate',
        default=True,
        help='Include SSL certificate'
    )
    
    has_backup = fields.Boolean(
        string='Backup Service',
        default=True,
        help='Include backup service'
    )
    
    has_support = fields.Boolean(
        string='Support Service',
        default=True,
        help='Include support service'
    )
    
    support_level = fields.Selection([
        ('basic', 'Basic'),
        ('standard', 'Standard'),
        ('premium', 'Premium'),
    ], string='Support Level', default='basic')
    
    # Technical
    template_db = fields.Char(
        string='Template Database',
        help='Template database for new instances'
    )
    
    server_id = fields.Many2one(
        'saas.server',
        string='Default Server',
        help='Default server for this plan'
    )
    
    active = fields.Boolean(
        string='Active',
        default=True,
        help='Uncheck to archive the plan'
    )

    # One2many relationships
    client_ids = fields.One2many(
        'saas.client',
        'plan_id',
        string='Clients',
        help='Clients using this plan'
    )

    instance_ids = fields.One2many(
        'saas.instance',
        'plan_id',
        string='Instances',
        help='Instances using this plan'
    )

    # Computed fields
    client_count = fields.Integer(
        string='Client Count',
        compute='_compute_client_count',
        help='Number of clients using this plan'
    )

    instance_count = fields.Integer(
        string='Instance Count',
        compute='_compute_instance_count',
        help='Number of instances using this plan'
    )
    
    @api.depends('name', 'code')
    def _compute_display_name(self):
        for plan in self:
            if plan.code:
                plan.display_name = f"[{plan.code}] {plan.name}"
            else:
                plan.display_name = plan.name
    
    def _compute_client_count(self):
        for plan in self:
            plan.client_count = len(plan.client_ids)

    def _compute_instance_count(self):
        for plan in self:
            plan.instance_count = len(plan.instance_ids)
    
    @api.model
    def create(self, vals):
        """Ensure plan code is unique"""
        if vals.get('code'):
            existing = self.search([('code', '=', vals['code'])])
            if existing:
                raise ValueError(f"Plan code '{vals['code']}' already exists")
        return super().create(vals)
    
    def write(self, vals):
        """Ensure plan code is unique"""
        if vals.get('code'):
            for plan in self:
                existing = self.search([
                    ('code', '=', vals['code']),
                    ('id', '!=', plan.id)
                ])
                if existing:
                    raise ValueError(f"Plan code '{vals['code']}' already exists")
        return super().write(vals)
    
    def action_activate(self):
        """Activate the plan"""
        self.write({'state': 'active'})
    
    def action_deprecate(self):
        """Deprecate the plan"""
        self.write({'state': 'deprecated'})
    
    def action_archive_plan(self):
        """Archive the plan"""
        self.write({'state': 'archived', 'active': False})
    
    def action_reset_to_draft(self):
        """Reset plan to draft state"""
        self.write({'state': 'draft'})

    def action_view_clients(self):
        """View clients using this plan"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Clients',
            'res_model': 'saas.client',
            'view_mode': 'list,form',
            'domain': [('plan_id', '=', self.id)],
            'context': {'default_plan_id': self.id}
        }

    def action_view_instances(self):
        """View instances using this plan"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Plan Instances',
            'res_model': 'saas.instance',
            'view_mode': 'list,form',
            'domain': [('plan_id', '=', self.id)],
            'context': {'default_plan_id': self.id}
        }

    @api.constrains('max_users', 'max_storage_gb', 'max_databases')
    def _check_limits(self):
        for plan in self:
            if plan.max_users < 1:
                raise ValueError("Max users must be at least 1")
            if plan.max_storage_gb < 0.1:
                raise ValueError("Max storage must be at least 0.1 GB")
            if plan.max_databases < 1:
                raise ValueError("Max databases must be at least 1")
