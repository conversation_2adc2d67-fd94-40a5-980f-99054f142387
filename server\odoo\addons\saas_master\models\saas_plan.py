# -*- coding: utf-8 -*-

from odoo import models, fields, api


class SaasPlan(models.Model):
    _name = 'saas.plan'
    _description = '<PERSON><PERSON>i D<PERSON>ch Vụ SaaS'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'sequence, name'

    name = fields.Char(
        string='Tên <PERSON>',
        required=True,
        tracking=True,
        help='Tên của gói dịch vụ'
    )

    code = fields.Char(
        string='Mã Gói',
        required=True,
        tracking=True,
        help='Mã duy nhất cho gói dịch vụ'
    )

    description = fields.Text(
        string='Mô Tả',
        help='Mô tả chi tiết về gói dịch vụ'
    )

    sequence = fields.Integer(
        string='Thứ Tự',
        default=10,
        help='Thứ tự sắp xếp các gói dịch vụ'
    )

    state = fields.Selection([
        ('draft', 'Nháp'),
        ('active', 'Hoạt Động'),
        ('deprecated', 'Lỗi Thời'),
        ('archived', '<PERSON><PERSON><PERSON>'),
    ], string='Trạng Thái', default='draft', required=True, tracking=True)
    
    # Pricing
    price = fields.Float(
        string='Giá',
        digits='Product Price',
        tracking=True,
        help='Giá hàng tháng cho gói dịch vụ này'
    )

    currency_id = fields.Many2one(
        'res.currency',
        string='Tiền Tệ',
        default=lambda self: self.env.company.currency_id,
        required=True,
        help='Đơn vị tiền tệ cho gói dịch vụ'
    )

    billing_cycle = fields.Selection([
        ('monthly', 'Hàng Tháng'),
        ('quarterly', 'Hàng Quý'),
        ('yearly', 'Hàng Năm'),
    ], string='Chu Kỳ Thanh Toán', default='monthly', required=True)
    
    # Plan Limits
    max_users = fields.Integer(
        string='Số Người Dùng Tối Đa',
        default=1,
        help='Số lượng người dùng tối đa được phép'
    )

    max_storage_gb = fields.Float(
        string='Dung Lượng Tối Đa (GB)',
        default=1.0,
        help='Dung lượng lưu trữ tối đa tính bằng GB'
    )

    max_databases = fields.Integer(
        string='Số Database Tối Đa',
        default=1,
        help='Số lượng database tối đa được phép'
    )
    
    # Features
    features = fields.Text(
        string='Tính Năng',
        help='Danh sách các tính năng bao gồm trong gói này'
    )

    has_custom_domain = fields.Boolean(
        string='Tên Miền Tùy Chỉnh',
        default=False,
        help='Cho phép sử dụng tên miền tùy chỉnh'
    )

    has_ssl = fields.Boolean(
        string='Chứng Chỉ SSL',
        default=True,
        help='Bao gồm chứng chỉ SSL'
    )

    has_backup = fields.Boolean(
        string='Dịch Vụ Sao Lưu',
        default=True,
        help='Bao gồm dịch vụ sao lưu dữ liệu'
    )

    has_support = fields.Boolean(
        string='Dịch Vụ Hỗ Trợ',
        default=True,
        help='Bao gồm dịch vụ hỗ trợ khách hàng'
    )

    support_level = fields.Selection([
        ('basic', 'Cơ Bản'),
        ('standard', 'Tiêu Chuẩn'),
        ('premium', 'Cao Cấp'),
    ], string='Mức Độ Hỗ Trợ', default='basic')
    
    # Technical
    template_db = fields.Char(
        string='Database Mẫu',
        help='Database mẫu cho các instance mới'
    )

    server_id = fields.Many2one(
        'saas.server',
        string='Server Mặc Định',
        help='Server mặc định cho gói dịch vụ này'
    )

    active = fields.Boolean(
        string='Hoạt Động',
        default=True,
        help='Bỏ chọn để lưu trữ gói dịch vụ'
    )

    # One2many relationships
    client_ids = fields.One2many(
        'saas.client',
        'plan_id',
        string='Khách Hàng',
        help='Khách hàng sử dụng gói dịch vụ này'
    )

    instance_ids = fields.One2many(
        'saas.instance',
        'plan_id',
        string='Instances',
        help='Instances sử dụng gói dịch vụ này'
    )

    # Computed fields
    client_count = fields.Integer(
        string='Số Lượng Khách Hàng',
        compute='_compute_client_count',
        help='Số lượng khách hàng sử dụng gói dịch vụ này'
    )

    instance_count = fields.Integer(
        string='Số Lượng Instances',
        compute='_compute_instance_count',
        help='Số lượng instances sử dụng gói dịch vụ này'
    )
    
    @api.depends('name', 'code')
    def _compute_display_name(self):
        for plan in self:
            if plan.code:
                plan.display_name = f"[{plan.code}] {plan.name}"
            else:
                plan.display_name = plan.name
    
    def _compute_client_count(self):
        for plan in self:
            plan.client_count = len(plan.client_ids)

    def _compute_instance_count(self):
        for plan in self:
            plan.instance_count = len(plan.instance_ids)
    
    @api.model
    def create(self, vals):
        """Đảm bảo mã gói dịch vụ là duy nhất"""
        if vals.get('code'):
            existing = self.search([('code', '=', vals['code'])])
            if existing:
                raise ValueError(f"Mã gói '{vals['code']}' đã tồn tại")
        return super().create(vals)

    def write(self, vals):
        """Đảm bảo mã gói dịch vụ là duy nhất"""
        if vals.get('code'):
            for plan in self:
                existing = self.search([
                    ('code', '=', vals['code']),
                    ('id', '!=', plan.id)
                ])
                if existing:
                    raise ValueError(f"Mã gói '{vals['code']}' đã tồn tại")
        return super().write(vals)

    def action_activate(self):
        """Kích hoạt gói dịch vụ"""
        self.write({'state': 'active'})

    def action_deprecate(self):
        """Đánh dấu gói dịch vụ lỗi thời"""
        self.write({'state': 'deprecated'})

    def action_archive_plan(self):
        """Lưu trữ gói dịch vụ"""
        self.write({'state': 'archived', 'active': False})

    def action_reset_to_draft(self):
        """Đặt lại gói dịch vụ về trạng thái nháp"""
        self.write({'state': 'draft'})

    def action_view_clients(self):
        """Xem khách hàng sử dụng gói dịch vụ này"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Khách Hàng',
            'res_model': 'saas.client',
            'view_mode': 'list,form',
            'domain': [('plan_id', '=', self.id)],
            'context': {'default_plan_id': self.id}
        }

    def action_view_instances(self):
        """Xem instances sử dụng gói dịch vụ này"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Instances Gói Dịch Vụ',
            'res_model': 'saas.instance',
            'view_mode': 'list,form',
            'domain': [('plan_id', '=', self.id)],
            'context': {'default_plan_id': self.id}
        }

    @api.constrains('max_users', 'max_storage_gb', 'max_databases')
    def _check_limits(self):
        for plan in self:
            if plan.max_users < 1:
                raise ValueError("Số người dùng tối đa phải ít nhất là 1")
            if plan.max_storage_gb < 0.1:
                raise ValueError("Dung lượng tối đa phải ít nhất là 0.1 GB")
            if plan.max_databases < 1:
                raise ValueError("Số database tối đa phải ít nhất là 1")
