<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- SaaS Server List View -->
    <record id="view_saas_server_list" model="ir.ui.view">
        <field name="name">saas.server.list</field>
        <field name="model">saas.server</field>
        <field name="arch" type="xml">
            <list string="SaaS Servers">
                <field name="name"/>
                <field name="code"/>
                <field name="ip_address"/>
                <field name="domain"/>
                <field name="instance_count"/>
                <field name="cpu_usage_percent" widget="percentage"/>
                <field name="ram_usage_percent" widget="percentage"/>
                <field name="storage_usage_percent" widget="percentage"/>
                <field name="load_percentage" widget="percentage"/>
                <field name="state" widget="badge" 
                       decoration-success="state == 'active'"
                       decoration-warning="state == 'maintenance'"
                       decoration-danger="state == 'inactive'"
                       decoration-info="state == 'draft'"/>
                <field name="last_ping"/>
            </list>
        </field>
    </record>

    <!-- SaaS Server Form View -->
    <record id="view_saas_server_form" model="ir.ui.view">
        <field name="name">saas.server.form</field>
        <field name="model">saas.server</field>
        <field name="arch" type="xml">
            <form string="SaaS Server">
                <header>
                    <button name="action_activate" string="Activate" type="object" 
                            class="btn-primary" invisible="state == 'active'"/>
                    <button name="action_maintenance" string="Maintenance" type="object" 
                            class="btn-warning" invisible="state == 'maintenance'"/>
                    <button name="action_deactivate" string="Deactivate" type="object" 
                            class="btn-danger" invisible="state == 'inactive'"/>
                    <button name="action_ping_server" string="Ping Server" type="object" 
                            class="btn-secondary"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,active,maintenance,inactive"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_instances" type="object" class="oe_stat_button" icon="fa-database">
                            <field name="instance_count" widget="statinfo" string="Instances"/>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="name" placeholder="Server Name"/>
                            <field name="code" placeholder="SERVER_CODE"/>
                            <field name="ip_address" placeholder="*************"/>
                            <field name="domain" placeholder="myserver.com"/>
                            <field name="port"/>
                        </group>
                        <group>
                            <field name="datacenter"/>
                            <field name="country_id"/>
                            <field name="timezone"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Server Specs">
                            <group>
                                <group string="Hardware">
                                    <field name="cpu_cores"/>
                                    <field name="ram_gb"/>
                                    <field name="storage_gb"/>
                                </group>
                                <group string="Software">
                                    <field name="odoo_version"/>
                                    <field name="operating_system"/>
                                    <field name="database_type"/>
                                </group>
                            </group>
                            <group>
                                <group string="Limits">
                                    <field name="max_instances"/>
                                    <field name="max_databases"/>
                                </group>
                                <group string="Security">
                                    <field name="ssl_enabled"/>
                                    <field name="firewall_enabled"/>
                                    <field name="backup_enabled"/>
                                </group>
                            </group>
                        </page>
                        <page string="Resource Usage">
                            <group>
                                <group string="Current Usage">
                                    <field name="cpu_usage_percent" widget="percentage"/>
                                    <field name="ram_usage_percent" widget="percentage"/>
                                    <field name="storage_used_gb"/>
                                    <field name="storage_usage_percent" widget="percentage"/>
                                    <field name="load_percentage" widget="percentage"/>
                                </group>
                                <group string="Monitoring">
                                    <field name="last_ping" readonly="1"/>
                                    <field name="uptime_days" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        <page string="Description">
                            <field name="description" placeholder="Server description and notes..."/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- SaaS Server Kanban View -->
    <record id="view_saas_server_kanban" model="ir.ui.view">
        <field name="name">saas.server.kanban</field>
        <field name="model">saas.server</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <field name="name"/>
                <field name="code"/>
                <field name="ip_address"/>
                <field name="state"/>
                <field name="instance_count"/>
                <field name="load_percentage"/>
                <templates>
                    <t t-name="card">
                        <div class="oe_kanban_content">
                            <div class="row">
                                <div class="col-8">
                                    <strong><field name="name"/></strong>
                                </div>
                                <div class="col-4 text-end">
                                    <field name="state" widget="badge"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span class="text-muted">Code: </span><field name="code"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span class="text-muted">IP: </span><field name="ip_address"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <span class="text-muted">Instances: </span><field name="instance_count"/>
                                </div>
                                <div class="col-6">
                                    <span class="text-muted">Load: </span>
                                    <field name="load_percentage" widget="percentage"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- SaaS Server Search View -->
    <record id="view_saas_server_search" model="ir.ui.view">
        <field name="name">saas.server.search</field>
        <field name="model">saas.server</field>
        <field name="arch" type="xml">
            <search string="Search SaaS Servers">
                <field name="name" string="Server Name"/>
                <field name="code"/>
                <field name="ip_address"/>
                <field name="domain"/>
                <field name="datacenter"/>
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Active Servers" name="active_servers" domain="[('state', '=', 'active')]"/>
                <filter string="Maintenance" name="maintenance" domain="[('state', '=', 'maintenance')]"/>
                <filter string="Inactive Servers" name="inactive_servers" domain="[('state', '=', 'inactive')]"/>
                <separator/>
                <filter string="High CPU Usage" name="high_cpu" domain="[('cpu_usage_percent', '&gt;=', 80)]"/>
                <filter string="High RAM Usage" name="high_ram" domain="[('ram_usage_percent', '&gt;=', 80)]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Country" name="group_country" context="{'group_by': 'country_id'}"/>
                    <filter string="Datacenter" name="group_datacenter" context="{'group_by': 'datacenter'}"/>
                    <filter string="Odoo Version" name="group_version" context="{'group_by': 'odoo_version'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- SaaS Server Action -->
    <record id="action_saas_server" model="ir.actions.act_window">
        <field name="name">SaaS Servers</field>
        <field name="res_model">saas.server</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="search_view_id" ref="view_saas_server_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Add your first SaaS server!
            </p>
            <p>
                Servers host your SaaS instances and databases.
                Configure server specifications, monitor resource usage, and manage hosting infrastructure.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_saas_server"
              name="Servers"
              parent="menu_saas_master_root"
              action="action_saas_server"
              sequence="40"/>
</odoo>
