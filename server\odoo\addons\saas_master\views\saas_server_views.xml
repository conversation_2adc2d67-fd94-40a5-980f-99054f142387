<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- SaaS Server List View -->
    <record id="view_saas_server_list" model="ir.ui.view">
        <field name="name">saas.server.list</field>
        <field name="model">saas.server</field>
        <field name="arch" type="xml">
            <list string="Servers SaaS" default_order="name" class="o_list_view_sidebar_responsive">
                <field name="name" string="Tên Server" width="150px"/>
                <field name="code" string="Mã" width="80px"/>
                <field name="ip_address" string="IP Address" width="120px"/>
                <field name="domain" string="Domain" width="150px"/>
                <field name="instance_count" string="Instances" width="80px"/>
                <field name="cpu_usage_percent" string="CPU %" widget="percentage" width="70px"/>
                <field name="ram_usage_percent" string="RAM %" widget="percentage" width="70px"/>
                <field name="storage_usage_percent" string="Storage %" widget="percentage" width="80px"/>
                <field name="load_percentage" string="Load %" widget="percentage" width="70px"/>
                <field name="state" string="Trạng Thái" widget="badge" width="100px"
                       decoration-success="state == 'active'"
                       decoration-warning="state == 'maintenance'"
                       decoration-danger="state == 'inactive'"
                       decoration-info="state == 'draft'"/>
                <field name="last_ping" string="Ping Cuối" width="120px"/>
            </list>
        </field>
    </record>

    <!-- SaaS Server Form View -->
    <record id="view_saas_server_form" model="ir.ui.view">
        <field name="name">saas.server.form</field>
        <field name="model">saas.server</field>
        <field name="arch" type="xml">
            <form string="Server SaaS" class="o_form_view_sidebar_optimized">
                <header>
                    <div class="d-flex flex-wrap align-items-center justify-content-start gap-2 mb-2">
                        <button name="action_activate" string="Kích Hoạt" type="object"
                                class="btn btn-primary me-2" invisible="state == 'active'"/>
                        <button name="action_maintenance" string="Bảo Trì" type="object"
                                class="btn btn-warning me-2" invisible="state == 'maintenance'"/>
                        <button name="action_deactivate" string="Vô Hiệu Hóa" type="object"
                                class="btn btn-danger me-2" invisible="state == 'inactive'"/>
                        <button name="action_ping_server" string="Ping Server" type="object"
                                class="btn btn-secondary me-2"/>
                    </div>
                    <field name="state" widget="statusbar" statusbar_visible="draft,active,maintenance,inactive"/>
                </header>
                <sheet class="o_form_sheet_sidebar_responsive">
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_instances" type="object" class="oe_stat_button" icon="fa-database">
                            <field name="instance_count" widget="statinfo" string="Instances"/>
                        </button>
                    </div>
                    <div class="row">
                        <div class="col-12 col-md-6">
                            <group string="Thông Tin Cơ Bản">
                                <field name="name" placeholder="Tên Server"/>
                                <field name="code" placeholder="MA_SERVER"/>
                                <field name="ip_address" placeholder="*************"/>
                                <field name="domain" placeholder="myserver.com"/>
                                <field name="port"/>
                            </group>
                        </div>
                        <div class="col-12 col-md-6">
                            <group string="Vị Trí và Cấu Hình">
                                <field name="datacenter"/>
                                <field name="country_id"/>
                                <field name="timezone"/>
                                <field name="active"/>
                            </group>
                        </div>
                    </div>
                    <notebook>
                        <page string="Thông Số Server">
                            <div class="row">
                                <div class="col-12 col-lg-6">
                                    <group string="Phần Cứng">
                                        <field name="cpu_cores"/>
                                        <field name="ram_gb"/>
                                        <field name="storage_gb"/>
                                    </group>
                                    <group string="Giới Hạn">
                                        <field name="max_instances"/>
                                        <field name="max_databases"/>
                                    </group>
                                </div>
                                <div class="col-12 col-lg-6">
                                    <group string="Phần Mềm">
                                        <field name="odoo_version"/>
                                        <field name="operating_system"/>
                                        <field name="database_type"/>
                                    </group>
                                    <group string="Bảo Mật">
                                        <field name="ssl_enabled"/>
                                        <field name="firewall_enabled"/>
                                        <field name="backup_enabled"/>
                                    </group>
                                </div>
                            </div>
                        </page>
                        <page string="Sử Dụng Tài Nguyên">
                            <group>
                                <group string="Sử Dụng Hiện Tại">
                                    <field name="cpu_usage_percent" widget="percentage"/>
                                    <field name="ram_usage_percent" widget="percentage"/>
                                    <field name="storage_used_gb"/>
                                    <field name="storage_usage_percent" widget="percentage"/>
                                    <field name="load_percentage" widget="percentage"/>
                                </group>
                                <group string="Giám Sát">
                                    <field name="last_ping" readonly="1"/>
                                    <field name="uptime_days" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        <page string="Mô Tả">
                            <field name="description" placeholder="Mô tả server và ghi chú..."/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- SaaS Server Kanban View -->
    <record id="view_saas_server_kanban" model="ir.ui.view">
        <field name="name">saas.server.kanban</field>
        <field name="model">saas.server</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile o_kanban_sidebar_responsive" default_group_by="state">
                <field name="name"/>
                <field name="code"/>
                <field name="ip_address"/>
                <field name="state"/>
                <field name="instance_count"/>
                <field name="load_percentage"/>
                <field name="cpu_usage_percent"/>
                <field name="ram_usage_percent"/>
                <templates>
                    <t t-name="card">
                        <div class="oe_kanban_content p-3">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div class="flex-grow-1">
                                    <strong class="fs-6"><field name="name"/></strong>
                                    <div class="text-muted small">
                                        <i class="fa fa-code me-1"></i><field name="code"/>
                                    </div>
                                </div>
                                <div class="ms-2">
                                    <field name="state" widget="badge"/>
                                </div>
                            </div>

                            <div class="mb-2">
                                <div class="text-muted small">
                                    <i class="fa fa-globe me-1"></i><field name="ip_address"/>
                                </div>
                            </div>

                            <div class="row g-2 small">
                                <div class="col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fa fa-database me-1 text-primary"></i>
                                        <span><field name="instance_count"/> Instances</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fa fa-tachometer me-1 text-warning"></i>
                                        <span>Load: <field name="load_percentage" widget="percentage"/></span>
                                    </div>
                                </div>
                            </div>

                            <div class="row g-2 small mt-1">
                                <div class="col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fa fa-microchip me-1 text-info"></i>
                                        <span>CPU: <field name="cpu_usage_percent" widget="percentage"/></span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-flex align-items-center">
                                        <i class="fa fa-memory me-1 text-success"></i>
                                        <span>RAM: <field name="ram_usage_percent" widget="percentage"/></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- SaaS Server Search View -->
    <record id="view_saas_server_search" model="ir.ui.view">
        <field name="name">saas.server.search</field>
        <field name="model">saas.server</field>
        <field name="arch" type="xml">
            <search string="Tìm Kiếm Servers SaaS">
                <field name="name" string="Tên Server" filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
                <field name="code" string="Mã Server"/>
                <field name="ip_address" string="Địa Chỉ IP"/>
                <field name="domain" string="Tên Miền"/>
                <field name="datacenter" string="Trung Tâm Dữ Liệu"/>
                <separator/>
                <filter string="Đang Hoạt Động" name="active" domain="[('active', '=', True)]" help="Servers đang hoạt động"/>
                <filter string="Không Hoạt Động" name="inactive" domain="[('active', '=', False)]" help="Servers không hoạt động"/>
                <separator/>
                <filter string="Nháp" name="draft" domain="[('state', '=', 'draft')]" help="Servers ở trạng thái nháp"/>
                <filter string="Servers Hoạt Động" name="active_servers" domain="[('state', '=', 'active')]" help="Servers đang hoạt động"/>
                <filter string="Bảo Trì" name="maintenance" domain="[('state', '=', 'maintenance')]" help="Servers đang bảo trì"/>
                <filter string="Servers Không Hoạt Động" name="inactive_servers" domain="[('state', '=', 'inactive')]" help="Servers không hoạt động"/>
                <separator/>
                <filter string="CPU Sử Dụng Cao" name="high_cpu" domain="[('cpu_usage_percent', '&gt;=', 80)]" help="Servers có CPU sử dụng >= 80%"/>
                <filter string="RAM Sử Dụng Cao" name="high_ram" domain="[('ram_usage_percent', '&gt;=', 80)]" help="Servers có RAM sử dụng >= 80%"/>
                <group expand="0" string="Nhóm Theo">
                    <filter string="Trạng Thái" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Quốc Gia" name="group_country" context="{'group_by': 'country_id'}"/>
                    <filter string="Trung Tâm Dữ Liệu" name="group_datacenter" context="{'group_by': 'datacenter'}"/>
                    <filter string="Phiên Bản Odoo" name="group_version" context="{'group_by': 'odoo_version'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- SaaS Server Action -->
    <record id="action_saas_server" model="ir.actions.act_window">
        <field name="name">Servers SaaS</field>
        <field name="res_model">saas.server</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="search_view_id" ref="view_saas_server_search"/>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Thêm server SaaS đầu tiên của bạn!
            </p>
            <p>
                Servers lưu trữ các instances và databases SaaS của bạn.
                Cấu hình thông số server, giám sát việc sử dụng tài nguyên, và quản lý hạ tầng lưu trữ.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_saas_server"
              name="Servers"
              parent="menu_saas_master_root"
              action="action_saas_server"
              sequence="40"/>
</odoo>
