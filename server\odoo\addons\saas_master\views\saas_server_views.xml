<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- SaaS Server List View -->
    <record id="view_saas_server_list" model="ir.ui.view">
        <field name="name">saas.server.list</field>
        <field name="model">saas.server</field>
        <field name="arch" type="xml">
            <list string="Servers SaaS" default_order="name">
                <field name="name"/>
                <field name="code"/>
                <field name="ip_address"/>
                <field name="domain"/>
                <field name="instance_count"/>
                <field name="cpu_usage_percent" widget="percentage"/>
                <field name="ram_usage_percent" widget="percentage"/>
                <field name="storage_usage_percent" widget="percentage"/>
                <field name="load_percentage" widget="percentage"/>
                <field name="state" widget="badge"
                       decoration-success="state == 'active'"
                       decoration-warning="state == 'maintenance'"
                       decoration-danger="state == 'inactive'"
                       decoration-info="state == 'draft'"/>
                <field name="last_ping"/>
            </list>
        </field>
    </record>

    <!-- SaaS Server Form View -->
    <record id="view_saas_server_form" model="ir.ui.view">
        <field name="name">saas.server.form</field>
        <field name="model">saas.server</field>
        <field name="arch" type="xml">
            <form string="Server SaaS" class="o_form_view_sidebar_optimized">
                <header class="o_form_header_horizontal">
                    <button name="action_activate" string="Kích Hoạt" type="object"
                            class="btn-primary" invisible="state == 'active'"/>
                    <button name="action_maintenance" string="Bảo Trì" type="object"
                            class="btn-warning" invisible="state == 'maintenance'"/>
                    <button name="action_deactivate" string="Vô Hiệu Hóa" type="object"
                            class="btn-danger" invisible="state == 'inactive'"/>
                    <button name="action_ping_server" string="Ping Server" type="object"
                            class="btn-secondary"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,active,maintenance,inactive"/>
                </header>
                <sheet class="o_form_sheet_sidebar_responsive">
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_instances" type="object" class="oe_stat_button" icon="fa-database">
                            <field name="instance_count" widget="statinfo" string="Instances"/>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="name" placeholder="Tên Server"/>
                            <field name="code" placeholder="MA_SERVER"/>
                            <field name="ip_address" placeholder="*************"/>
                            <field name="domain" placeholder="myserver.com"/>
                            <field name="port"/>
                        </group>
                        <group>
                            <field name="datacenter"/>
                            <field name="country_id"/>
                            <field name="timezone"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Thông Số Server">
                            <group>
                                <group string="Phần Cứng">
                                    <field name="cpu_cores"/>
                                    <field name="ram_gb"/>
                                    <field name="storage_gb"/>
                                </group>
                                <group string="Phần Mềm">
                                    <field name="odoo_version"/>
                                    <field name="operating_system"/>
                                    <field name="database_type"/>
                                </group>
                            </group>
                            <group>
                                <group string="Giới Hạn">
                                    <field name="max_instances"/>
                                    <field name="max_databases"/>
                                </group>
                                <group string="Bảo Mật">
                                    <field name="ssl_enabled"/>
                                    <field name="firewall_enabled"/>
                                    <field name="backup_enabled"/>
                                </group>
                            </group>
                        </page>
                        <page string="Sử Dụng Tài Nguyên">
                            <group>
                                <group string="Sử Dụng Hiện Tại">
                                    <field name="cpu_usage_percent" widget="percentage"/>
                                    <field name="ram_usage_percent" widget="percentage"/>
                                    <field name="storage_used_gb"/>
                                    <field name="storage_usage_percent" widget="percentage"/>
                                    <field name="load_percentage" widget="percentage"/>
                                </group>
                                <group string="Giám Sát">
                                    <field name="last_ping" readonly="1"/>
                                    <field name="uptime_days" readonly="1"/>
                                </group>
                            </group>
                        </page>
                        <page string="Mô Tả">
                            <field name="description" placeholder="Mô tả server và ghi chú..."/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- SaaS Server Kanban View -->
    <record id="view_saas_server_kanban" model="ir.ui.view">
        <field name="name">saas.server.kanban</field>
        <field name="model">saas.server</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile o_kanban_sidebar_responsive">
                <field name="name"/>
                <field name="code"/>
                <field name="ip_address"/>
                <field name="state"/>
                <field name="instance_count"/>
                <field name="load_percentage"/>
                <templates>
                    <t t-name="card">
                        <div class="oe_kanban_content">
                            <div class="row">
                                <div class="col-8">
                                    <strong><field name="name"/></strong>
                                </div>
                                <div class="col-4 text-end">
                                    <field name="state" widget="badge"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span class="text-muted">Mã: </span><field name="code"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span class="text-muted">IP: </span><field name="ip_address"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6">
                                    <span class="text-muted">Instances: </span><field name="instance_count"/>
                                </div>
                                <div class="col-6">
                                    <span class="text-muted">Tải: </span>
                                    <field name="load_percentage" widget="percentage"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- SaaS Server Search View -->
    <record id="view_saas_server_search" model="ir.ui.view">
        <field name="name">saas.server.search</field>
        <field name="model">saas.server</field>
        <field name="arch" type="xml">
            <search string="Tìm Kiếm Servers SaaS">
                <field name="name" string="Tên Server" filter_domain="['|', ('name', 'ilike', self), ('code', 'ilike', self)]"/>
                <field name="code" string="Mã Server"/>
                <field name="ip_address" string="Địa Chỉ IP"/>
                <field name="domain" string="Tên Miền"/>
                <field name="datacenter" string="Trung Tâm Dữ Liệu"/>
                <separator/>
                <filter string="Đang Hoạt Động" name="active" domain="[('active', '=', True)]" help="Servers đang hoạt động"/>
                <filter string="Không Hoạt Động" name="inactive" domain="[('active', '=', False)]" help="Servers không hoạt động"/>
                <separator/>
                <filter string="Nháp" name="draft" domain="[('state', '=', 'draft')]" help="Servers ở trạng thái nháp"/>
                <filter string="Servers Hoạt Động" name="active_servers" domain="[('state', '=', 'active')]" help="Servers đang hoạt động"/>
                <filter string="Bảo Trì" name="maintenance" domain="[('state', '=', 'maintenance')]" help="Servers đang bảo trì"/>
                <filter string="Servers Không Hoạt Động" name="inactive_servers" domain="[('state', '=', 'inactive')]" help="Servers không hoạt động"/>
                <separator/>
                <filter string="CPU Sử Dụng Cao" name="high_cpu" domain="[('cpu_usage_percent', '&gt;=', 80)]" help="Servers có CPU sử dụng >= 80%"/>
                <filter string="RAM Sử Dụng Cao" name="high_ram" domain="[('ram_usage_percent', '&gt;=', 80)]" help="Servers có RAM sử dụng >= 80%"/>
                <group expand="0" string="Nhóm Theo">
                    <filter string="Trạng Thái" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Quốc Gia" name="group_country" context="{'group_by': 'country_id'}"/>
                    <filter string="Trung Tâm Dữ Liệu" name="group_datacenter" context="{'group_by': 'datacenter'}"/>
                    <filter string="Phiên Bản Odoo" name="group_version" context="{'group_by': 'odoo_version'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- SaaS Server Action -->
    <record id="action_saas_server" model="ir.actions.act_window">
        <field name="name">Servers SaaS</field>
        <field name="res_model">saas.server</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="search_view_id" ref="view_saas_server_search"/>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Thêm server SaaS đầu tiên của bạn!
            </p>
            <p>
                Servers lưu trữ các instances và databases SaaS của bạn.
                Cấu hình thông số server, giám sát việc sử dụng tài nguyên, và quản lý hạ tầng lưu trữ.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_saas_server"
              name="Servers"
              parent="menu_saas_master_root"
              action="action_saas_server"
              sequence="40"/>
</odoo>
