# -*- coding: utf-8 -*-

from odoo import models, fields, api


class SaasServer(models.Model):
    _name = 'saas.server'
    _description = 'Server L<PERSON><PERSON>r<PERSON> SaaS'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(
        string='Tên Server',
        required=True,
        tracking=True,
        help='Tên của server lưu trữ'
    )

    code = fields.Char(
        string='Mã Server',
        required=True,
        tracking=True,
        help='Mã duy nhất cho server'
    )

    description = fields.Text(
        string='Mô Tả',
        help='Mô tả về server'
    )

    state = fields.Selection([
        ('draft', 'Nháp'),
        ('active', 'Hoạt Động'),
        ('maintenance', 'Bảo Trì'),
        ('inactive', 'Không Hoạt Động'),
    ], string='Trạng Thái', default='draft', required=True, tracking=True)

    # Server Details
    ip_address = fields.Char(
        string='Địa Chỉ IP',
        required=True,
        tracking=True,
        help='Địa chỉ IP của server'
    )

    domain = fields.Char(
        string='Tên <PERSON>n',
        required=True,
        tracking=True,
        help='Tên miền cơ sở cho instances (ví dụ: myserver.com)'
    )
    
    port = fields.Integer(
        string='Cổng',
        default=8069,
        help='Cổng server Odoo'
    )

    # Server Specifications
    cpu_cores = fields.Integer(
        string='Số Lõi CPU',
        default=2,
        help='Số lượng lõi CPU'
    )

    ram_gb = fields.Float(
        string='RAM (GB)',
        default=4.0,
        help='RAM tính bằng GB'
    )

    storage_gb = fields.Float(
        string='Dung Lượng (GB)',
        default=100.0,
        help='Tổng dung lượng lưu trữ tính bằng GB'
    )

    # Resource Usage
    cpu_usage_percent = fields.Float(
        string='% Sử Dụng CPU',
        default=0.0,
        help='Phần trăm sử dụng CPU hiện tại'
    )

    ram_usage_percent = fields.Float(
        string='% Sử Dụng RAM',
        default=0.0,
        help='Phần trăm sử dụng RAM hiện tại'
    )

    storage_used_gb = fields.Float(
        string='Dung Lượng Đã Dùng (GB)',
        default=0.0,
        help='Dung lượng hiện tại đã sử dụng tính bằng GB'
    )

    # Limits
    max_instances = fields.Integer(
        string='Số Instance Tối Đa',
        default=50,
        help='Số lượng instances tối đa mà server này có thể lưu trữ'
    )

    max_databases = fields.Integer(
        string='Số Database Tối Đa',
        default=100,
        help='Số lượng databases tối đa mà server này có thể lưu trữ'
    )

    # Configuration
    odoo_version = fields.Char(
        string='Phiên Bản Odoo',
        default='18.0',
        help='Phiên bản Odoo được cài đặt trên server này'
    )

    operating_system = fields.Char(
        string='Hệ Điều Hành',
        default='Ubuntu 22.04',
        help='Hệ điều hành của server'
    )

    database_type = fields.Selection([
        ('postgresql', 'PostgreSQL'),
        ('mysql', 'MySQL'),
    ], string='Loại Database', default='postgresql', required=True)

    # Security
    ssl_enabled = fields.Boolean(
        string='SSL Được Bật',
        default=True,
        help='Chứng chỉ SSL được kích hoạt'
    )

    firewall_enabled = fields.Boolean(
        string='Firewall Được Bật',
        default=True,
        help='Bảo vệ firewall được kích hoạt'
    )

    backup_enabled = fields.Boolean(
        string='Sao Lưu Được Bật',
        default=True,
        help='Sao lưu tự động được kích hoạt'
    )

    # Monitoring
    last_ping = fields.Datetime(
        string='Ping Cuối',
        help='Lần ping thành công cuối cùng tới server'
    )
    
    uptime_days = fields.Float(
        string='Thời Gian Hoạt Động (Ngày)',
        default=0.0,
        help='Thời gian hoạt động của server tính bằng ngày'
    )

    # Location
    datacenter = fields.Char(
        string='Trung Tâm Dữ Liệu',
        help='Vị trí trung tâm dữ liệu'
    )

    country_id = fields.Many2one(
        'res.country',
        string='Quốc Gia',
        help='Vị trí quốc gia của server'
    )

    timezone = fields.Selection(
        '_get_timezone_list',
        string='Múi Giờ',
        default='UTC',
        help='Múi giờ của server'
    )

    active = fields.Boolean(
        string='Hoạt Động',
        default=True,
        help='Bỏ chọn để lưu trữ server'
    )

    # One2many relationships
    instance_ids = fields.One2many(
        'saas.instance',
        'server_id',
        string='Instances',
        help='Instances được lưu trữ trên server này'
    )

    plan_ids = fields.One2many(
        'saas.plan',
        'server_id',
        string='Gói Mặc Định',
        help='Các gói sử dụng server này làm mặc định'
    )

    # Computed fields
    instance_count = fields.Integer(
        string='Số Lượng Instance',
        compute='_compute_instance_count',
        help='Số lượng instances trên server này'
    )

    storage_usage_percent = fields.Float(
        string='% Sử Dụng Dung Lượng',
        compute='_compute_storage_usage_percent',
        help='Phần trăm sử dụng dung lượng'
    )

    load_percentage = fields.Float(
        string='% Tải',
        compute='_compute_load_percentage',
        help='Phần trăm tải tổng thể của server'
    )

    @api.model
    def _get_timezone_list(self):
        """Lấy danh sách múi giờ"""
        return [
            ('UTC', 'UTC'),
            ('US/Eastern', 'US/Eastern'),
            ('US/Central', 'US/Central'),
            ('US/Mountain', 'US/Mountain'),
            ('US/Pacific', 'US/Pacific'),
            ('Europe/London', 'Europe/London'),
            ('Europe/Paris', 'Europe/Paris'),
            ('Europe/Berlin', 'Europe/Berlin'),
            ('Asia/Tokyo', 'Asia/Tokyo'),
            ('Asia/Shanghai', 'Asia/Shanghai'),
            ('Asia/Ho_Chi_Minh', 'Asia/Ho_Chi_Minh'),
        ]
    
    def _compute_instance_count(self):
        for server in self:
            server.instance_count = len(server.instance_ids)
    
    @api.depends('storage_used_gb', 'storage_gb')
    def _compute_storage_usage_percent(self):
        for server in self:
            if server.storage_gb > 0:
                server.storage_usage_percent = (server.storage_used_gb / server.storage_gb) * 100
            else:
                server.storage_usage_percent = 0
    
    @api.depends('cpu_usage_percent', 'ram_usage_percent', 'storage_usage_percent')
    def _compute_load_percentage(self):
        for server in self:
            # Calculate average load from CPU, RAM, and Storage usage
            total = server.cpu_usage_percent + server.ram_usage_percent + server.storage_usage_percent
            server.load_percentage = total / 3
    
    @api.model
    def create(self, vals):
        """Đảm bảo mã server là duy nhất"""
        if vals.get('code'):
            existing = self.search([('code', '=', vals['code'])])
            if existing:
                raise ValueError(f"Mã server '{vals['code']}' đã tồn tại")
        return super().create(vals)

    def write(self, vals):
        """Đảm bảo mã server là duy nhất"""
        if vals.get('code'):
            for server in self:
                existing = self.search([
                    ('code', '=', vals['code']),
                    ('id', '!=', server.id)
                ])
                if existing:
                    raise ValueError(f"Mã server '{vals['code']}' đã tồn tại")
        return super().write(vals)

    def action_activate(self):
        """Kích hoạt server"""
        self.write({'state': 'active'})

    def action_maintenance(self):
        """Đưa server vào chế độ bảo trì"""
        self.write({'state': 'maintenance'})

    def action_deactivate(self):
        """Vô hiệu hóa server"""
        self.write({'state': 'inactive'})

    def action_ping_server(self):
        """Ping server để kiểm tra kết nối"""
        # Ở đây bạn sẽ triển khai logic ping thực tế
        self.write({'last_ping': fields.Datetime.now()})
        return True

    def action_view_instances(self):
        """Xem instances trên server này"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Instances Server',
            'res_model': 'saas.instance',
            'view_mode': 'list,form',
            'domain': [('server_id', '=', self.id)],
            'context': {'default_server_id': self.id}
        }

    @api.constrains('max_instances', 'max_databases')
    def _check_limits(self):
        for server in self:
            if server.max_instances < 1:
                raise ValueError("Số instances tối đa phải ít nhất là 1")
            if server.max_databases < 1:
                raise ValueError("Số databases tối đa phải ít nhất là 1")

    @api.constrains('instance_count', 'max_instances')
    def _check_instance_limit(self):
        for server in self:
            if server.instance_count > server.max_instances:
                raise ValueError(f"Số lượng instance ({server.instance_count}) vượt quá giới hạn server ({server.max_instances})")
