# -*- coding: utf-8 -*-

from odoo import models, fields, api


class SaasServer(models.Model):
    _name = 'saas.server'
    _description = 'SaaS Hosting Server'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(
        string='Server Name',
        required=True,
        tracking=True,
        help='Name of the hosting server'
    )
    
    code = fields.Char(
        string='Server Code',
        required=True,
        tracking=True,
        help='Unique code for the server'
    )
    
    description = fields.Text(
        string='Description',
        help='Description of the server'
    )
    
    state = fields.Selection([
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('maintenance', 'Maintenance'),
        ('inactive', 'Inactive'),
    ], string='Status', default='draft', required=True, tracking=True)
    
    # Server Details
    ip_address = fields.Char(
        string='IP Address',
        required=True,
        tracking=True,
        help='Server IP address'
    )
    
    domain = fields.Char(
        string='Domain',
        required=True,
        tracking=True,
        help='Base domain for instances (e.g., myserver.com)'
    )
    
    port = fields.Integer(
        string='Port',
        default=8069,
        help='Odoo server port'
    )
    
    # Server Specifications
    cpu_cores = fields.Integer(
        string='CPU Cores',
        default=2,
        help='Number of CPU cores'
    )
    
    ram_gb = fields.Float(
        string='RAM (GB)',
        default=4.0,
        help='RAM in GB'
    )
    
    storage_gb = fields.Float(
        string='Storage (GB)',
        default=100.0,
        help='Total storage in GB'
    )
    
    # Resource Usage
    cpu_usage_percent = fields.Float(
        string='CPU Usage %',
        default=0.0,
        help='Current CPU usage percentage'
    )
    
    ram_usage_percent = fields.Float(
        string='RAM Usage %',
        default=0.0,
        help='Current RAM usage percentage'
    )
    
    storage_used_gb = fields.Float(
        string='Storage Used (GB)',
        default=0.0,
        help='Storage currently used in GB'
    )
    
    # Limits
    max_instances = fields.Integer(
        string='Max Instances',
        default=50,
        help='Maximum number of instances this server can host'
    )
    
    max_databases = fields.Integer(
        string='Max Databases',
        default=100,
        help='Maximum number of databases this server can host'
    )
    
    # Configuration
    odoo_version = fields.Char(
        string='Odoo Version',
        default='18.0',
        help='Odoo version installed on this server'
    )
    
    operating_system = fields.Char(
        string='Operating System',
        default='Ubuntu 22.04',
        help='Server operating system'
    )
    
    database_type = fields.Selection([
        ('postgresql', 'PostgreSQL'),
        ('mysql', 'MySQL'),
    ], string='Database Type', default='postgresql', required=True)
    
    # Security
    ssl_enabled = fields.Boolean(
        string='SSL Enabled',
        default=True,
        help='SSL certificate enabled'
    )
    
    firewall_enabled = fields.Boolean(
        string='Firewall Enabled',
        default=True,
        help='Firewall protection enabled'
    )
    
    backup_enabled = fields.Boolean(
        string='Backup Enabled',
        default=True,
        help='Automatic backup enabled'
    )
    
    # Monitoring
    last_ping = fields.Datetime(
        string='Last Ping',
        help='Last successful ping to server'
    )
    
    uptime_days = fields.Float(
        string='Uptime (Days)',
        default=0.0,
        help='Server uptime in days'
    )
    
    # Location
    datacenter = fields.Char(
        string='Datacenter',
        help='Datacenter location'
    )
    
    country_id = fields.Many2one(
        'res.country',
        string='Country',
        help='Server country location'
    )
    
    timezone = fields.Selection(
        '_get_timezone_list',
        string='Timezone',
        default='UTC',
        help='Server timezone'
    )
    
    active = fields.Boolean(
        string='Active',
        default=True,
        help='Uncheck to archive the server'
    )

    # One2many relationships
    instance_ids = fields.One2many(
        'saas.instance',
        'server_id',
        string='Instances',
        help='Instances hosted on this server'
    )

    plan_ids = fields.One2many(
        'saas.plan',
        'server_id',
        string='Default Plans',
        help='Plans that use this server as default'
    )

    # Computed fields
    instance_count = fields.Integer(
        string='Instance Count',
        compute='_compute_instance_count',
        help='Number of instances on this server'
    )
    
    storage_usage_percent = fields.Float(
        string='Storage Usage %',
        compute='_compute_storage_usage_percent',
        help='Storage usage percentage'
    )
    
    load_percentage = fields.Float(
        string='Load %',
        compute='_compute_load_percentage',
        help='Overall server load percentage'
    )
    
    @api.model
    def _get_timezone_list(self):
        """Get list of timezones"""
        return [
            ('UTC', 'UTC'),
            ('US/Eastern', 'US/Eastern'),
            ('US/Central', 'US/Central'),
            ('US/Mountain', 'US/Mountain'),
            ('US/Pacific', 'US/Pacific'),
            ('Europe/London', 'Europe/London'),
            ('Europe/Paris', 'Europe/Paris'),
            ('Europe/Berlin', 'Europe/Berlin'),
            ('Asia/Tokyo', 'Asia/Tokyo'),
            ('Asia/Shanghai', 'Asia/Shanghai'),
            ('Asia/Ho_Chi_Minh', 'Asia/Ho_Chi_Minh'),
        ]
    
    def _compute_instance_count(self):
        for server in self:
            server.instance_count = len(server.instance_ids)
    
    @api.depends('storage_used_gb', 'storage_gb')
    def _compute_storage_usage_percent(self):
        for server in self:
            if server.storage_gb > 0:
                server.storage_usage_percent = (server.storage_used_gb / server.storage_gb) * 100
            else:
                server.storage_usage_percent = 0
    
    @api.depends('cpu_usage_percent', 'ram_usage_percent', 'storage_usage_percent')
    def _compute_load_percentage(self):
        for server in self:
            # Calculate average load from CPU, RAM, and Storage usage
            total = server.cpu_usage_percent + server.ram_usage_percent + server.storage_usage_percent
            server.load_percentage = total / 3
    
    @api.model
    def create(self, vals):
        """Ensure server code is unique"""
        if vals.get('code'):
            existing = self.search([('code', '=', vals['code'])])
            if existing:
                raise ValueError(f"Server code '{vals['code']}' already exists")
        return super().create(vals)
    
    def write(self, vals):
        """Ensure server code is unique"""
        if vals.get('code'):
            for server in self:
                existing = self.search([
                    ('code', '=', vals['code']),
                    ('id', '!=', server.id)
                ])
                if existing:
                    raise ValueError(f"Server code '{vals['code']}' already exists")
        return super().write(vals)
    
    def action_activate(self):
        """Activate the server"""
        self.write({'state': 'active'})
    
    def action_maintenance(self):
        """Put server in maintenance mode"""
        self.write({'state': 'maintenance'})
    
    def action_deactivate(self):
        """Deactivate the server"""
        self.write({'state': 'inactive'})
    
    def action_ping_server(self):
        """Ping the server to check connectivity"""
        # Here you would implement actual ping logic
        self.write({'last_ping': fields.Datetime.now()})
        return True
    
    def action_view_instances(self):
        """View instances on this server"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Server Instances',
            'res_model': 'saas.instance',
            'view_mode': 'list,form',
            'domain': [('server_id', '=', self.id)],
            'context': {'default_server_id': self.id}
        }
    
    @api.constrains('max_instances', 'max_databases')
    def _check_limits(self):
        for server in self:
            if server.max_instances < 1:
                raise ValueError("Max instances must be at least 1")
            if server.max_databases < 1:
                raise ValueError("Max databases must be at least 1")
    
    @api.constrains('instance_count', 'max_instances')
    def _check_instance_limit(self):
        for server in self:
            if server.instance_count > server.max_instances:
                raise ValueError(f"Instance count ({server.instance_count}) exceeds server limit ({server.max_instances})")
