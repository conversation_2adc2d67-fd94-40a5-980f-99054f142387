2025-06-27 15:35:42.866 +07 [4604] LOG:  database system was shut down at 2025-06-27 15:35:03 +07
2025-06-27 15:35:42.961 +07 [11056] LOG:  database system is ready to accept connections
2025-06-27 15:40:56.266 +07 [3588] ERROR:  operator does not exist: jsonb ~~ unknown at character 71
2025-06-27 15:40:56.266 +07 [3588] HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.
2025-06-27 15:40:56.266 +07 [3588] STATEMENT:  SELECT id, name, active, parent_id, action FROM ir_ui_menu WHERE name LIKE '%Multi-Channel%' OR name LIKE '%multichannel%' ORDER BY id
2025-06-27 15:41:09.298 +07 [26048] ERROR:  relation "ir_actions_act_window" does not exist at character 33
2025-06-27 15:41:09.298 +07 [26048] STATEMENT:  SELECT id, name, res_model FROM ir_actions_act_window WHERE res_model LIKE '%multichannel%'
2025-06-27 15:41:20.859 +07 [10660] ERROR:  column "im_status" does not exist at character 50
2025-06-27 15:41:20.859 +07 [10660] STATEMENT:  UPDATE res_users SET im_status = 'offline' WHERE im_status != 'offline'
2025-06-27 15:43:56.965 +07 [15640] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 15:43:56.966 +07 [14276] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 15:43:56.966 +07 [16524] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 15:45:33.201 +07 [17960] ERROR:  operator does not exist: jsonb ~~ unknown at character 53
2025-06-27 15:45:33.201 +07 [17960] HINT:  No operator matches the given name and argument types. You might need to add explicit type casts.
2025-06-27 15:45:33.201 +07 [17960] STATEMENT:  SELECT name, category_id FROM res_groups WHERE name LIKE '%multichannel%'
2025-06-27 15:49:20.924 +07 [12264] ERROR:  could not serialize access due to concurrent update
2025-06-27 15:49:20.924 +07 [12264] STATEMENT:   UPDATE "bus_presence"
	                    SET "status" = "__tmp"."status"::VARCHAR
	                    FROM (VALUES (3, 'offline')) AS "__tmp"("id", "status")
	                    WHERE "bus_presence"."id" = "__tmp"."id"
	                
2025-06-27 15:50:05.588 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:50:05.588 +07 [18376] STATEMENT:  DROP TABLE "multichannel_product_sync_wizard" CASCADE
2025-06-27 15:50:20.631 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:50:20.631 +07 [18376] STATEMENT:  DROP TABLE "multichannel_product_sync_wizard" CASCADE
2025-06-27 15:50:35.672 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:50:35.672 +07 [18376] STATEMENT:  DROP TABLE "multichannel_product_sync_wizard" CASCADE
2025-06-27 15:50:50.703 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:50:50.703 +07 [18376] STATEMENT:  DROP TABLE "multichannel_product_sync_wizard" CASCADE
2025-06-27 15:51:05.739 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:51:05.739 +07 [18376] STATEMENT:  DROP TABLE "multichannel_shop_sync_wizard" CASCADE
2025-06-27 15:51:20.777 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:51:20.777 +07 [18376] STATEMENT:  DROP TABLE "multichannel_warehouse_mapping" CASCADE
2025-06-27 15:51:35.807 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:51:35.807 +07 [18376] STATEMENT:  DROP TABLE "multichannel_warehouse_mapping" CASCADE
2025-06-27 15:51:50.842 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:51:50.842 +07 [18376] STATEMENT:  DROP TABLE "multichannel_affiliate_transaction" CASCADE
2025-06-27 15:52:05.886 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:52:05.886 +07 [18376] STATEMENT:  DROP TABLE "multichannel_affiliate" CASCADE
2025-06-27 15:52:20.922 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:52:20.922 +07 [18376] STATEMENT:  DROP TABLE "multichannel_affiliate" CASCADE
2025-06-27 15:52:35.951 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:52:35.951 +07 [18376] STATEMENT:  DROP TABLE "multichannel_affiliate" CASCADE
2025-06-27 15:52:50.982 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:52:50.982 +07 [18376] STATEMENT:  DROP TABLE "multichannel_advertising" CASCADE
2025-06-27 15:53:06.023 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:53:06.023 +07 [18376] STATEMENT:  DROP TABLE "multichannel_transaction" CASCADE
2025-06-27 15:53:21.057 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:53:21.057 +07 [18376] STATEMENT:  DROP TABLE "multichannel_transaction" CASCADE
2025-06-27 15:53:36.092 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:53:36.092 +07 [18376] STATEMENT:  DROP TABLE "multichannel_product_mapping" CASCADE
2025-06-27 15:53:51.157 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:53:51.157 +07 [18376] STATEMENT:  DROP TABLE "multichannel_product_mapping" CASCADE
2025-06-27 15:54:06.197 +07 [18376] ERROR:  canceling statement due to lock timeout
2025-06-27 15:54:06.197 +07 [18376] STATEMENT:  DROP TABLE "multichannel_shop" CASCADE
2025-06-27 16:08:52.446 +07 [19356] ERROR:  relation "ir_sessions" does not exist at character 13
2025-06-27 16:08:52.446 +07 [19356] STATEMENT:  DELETE FROM ir_sessions
2025-06-27 16:09:08.533 +07 [18376] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.533 +07 [28080] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.534 +07 [2260] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.535 +07 [2260] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 16:09:08.535 +07 [17708] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.535 +07 [13320] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.536 +07 [17708] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 16:09:08.536 +07 [13320] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 16:09:08.537 +07 [25608] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.537 +07 [5900] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.537 +07 [6468] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.537 +07 [14392] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.537 +07 [5268] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.538 +07 [5900] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 16:09:08.538 +07 [14392] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 16:09:08.538 +07 [6468] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 16:09:08.538 +07 [5268] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 16:09:08.538 +07 [4604] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.539 +07 [17572] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.540 +07 [17572] LOG:  unexpected EOF on client connection with an open transaction
2025-06-27 16:09:08.540 +07 [13436] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.540 +07 [15440] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.540 +07 [27192] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:09:08.540 +07 [16860] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:15:10.678 +07 [18544] ERROR:  relation "ir_actions_act_window" does not exist at character 53
2025-06-27 16:15:10.678 +07 [18544] STATEMENT:  
	        SELECT name, res_model, type 
	        FROM ir_actions_act_window 
	        WHERE name ILIKE '%multichannel%'
	    
2025-06-27 16:15:26.130 +07 [7016] ERROR:  relation "ir_actions_actions" does not exist at character 57
2025-06-27 16:15:26.130 +07 [7016] STATEMENT:  
	        SELECT id, name, res_model, type 
	        FROM ir_actions_actions 
	        WHERE name ILIKE '%multichannel%'
	    
2025-06-27 16:15:36.496 +07 [22560] ERROR:  relation "ir_ui_menu_cache" does not exist at character 13
2025-06-27 16:15:36.496 +07 [22560] STATEMENT:  DELETE FROM ir_ui_menu_cache
2025-06-27 16:17:54.063 +07 [19652] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:17:54.064 +07 [15928] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:17:54.064 +07 [23192] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:29:41.631 +07 [16304] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:29:41.631 +07 [11120] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:29:41.631 +07 [13208] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:29:41.631 +07 [15632] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
2025-06-27 16:29:41.631 +07 [16492] LOG:  could not receive data from client: An existing connection was forcibly closed by the remote host.

	
