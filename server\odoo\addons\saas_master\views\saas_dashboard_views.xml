<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- SaaS Dashboard View -->
    <record id="view_saas_dashboard" model="ir.ui.view">
        <field name="name">saas.dashboard</field>
        <field name="model">saas.client</field>
        <field name="arch" type="xml">
            <form string="Bảng Điều Khiển SaaS" create="false" edit="false" delete="false" class="o_form_view_sidebar_optimized">
                <sheet class="o_form_sheet_sidebar_responsive">
                    <div class="o_dashboard">
                        <h1>B<PERSON>ng Đ<PERSON>ề<PERSON> Khiể<PERSON> SaaS Master</h1>

                        <!-- Key Metrics Row -->
                        <div class="row">
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h4>T<PERSON><PERSON></h4>
                                        <h2 id="total_clients">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h4>Instances Hoạt Động</h4>
                                        <h2 id="active_instances">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h4>Gói Có Sẵn</h4>
                                        <h2 id="total_plans">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <h4>Servers Hoạt Động</h4>
                                        <h2 id="active_servers">0</h2>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions Row -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h3>Thao Tác Nhanh</h3>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <button name="action_new_client" string="Khách Hàng Mới" type="object" class="btn btn-primary btn-lg btn-block"/>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <button name="action_new_instance" string="Instance Mới" type="object" class="btn btn-success btn-lg btn-block"/>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <button name="action_view_all_clients" string="Xem Tất Cả Khách Hàng" type="object" class="btn btn-info btn-lg btn-block"/>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <button name="action_view_all_instances" string="Xem Tất Cả Instances" type="object" class="btn btn-warning btn-lg btn-block"/>
                            </div>
                        </div>

                        <!-- Recent Activity Row -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Khách Hàng Gần Đây</h4>
                                    </div>
                                    <div class="card-body">
                                        <div id="recent_clients">
                                            <!-- Khách hàng gần đây sẽ được tải ở đây -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Instances Gần Đây</h4>
                                    </div>
                                    <div class="card-body">
                                        <div id="recent_instances">
                                            <!-- Instances gần đây sẽ được tải ở đây -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Server Status Row -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Trạng Thái Server</h4>
                                    </div>
                                    <div class="card-body">
                                        <div id="server_status">
                                            <!-- Trạng thái server sẽ được tải ở đây -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Dashboard Action -->
    <record id="action_saas_dashboard" model="ir.actions.act_window">
        <field name="name">Bảng Điều Khiển SaaS</field>
        <field name="res_model">saas.client</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_saas_dashboard"/>
        <field name="target">current</field>
        <field name="context">{'dashboard_mode': True}</field>
    </record>

    <!-- Customer Portal View -->
    <record id="view_saas_customer_portal" model="ir.ui.view">
        <field name="name">saas.customer.portal</field>
        <field name="model">saas.client</field>
        <field name="arch" type="xml">
            <form string="Cổng Khách Hàng" create="false" edit="false" delete="false" class="o_form_view_sidebar_optimized">
                <sheet class="o_form_sheet_sidebar_responsive">
                    <div class="o_customer_portal">
                        <h1>Chào Mừng Đến Cổng SaaS Của Bạn</h1>

                        <!-- Customer Info -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Thông Tin Tài Khoản Của Bạn</h4>
                                    </div>
                                    <div class="card-body">
                                        <field name="name" readonly="1"/>
                                        <field name="email" readonly="1"/>
                                        <field name="phone" readonly="1"/>
                                        <field name="plan_id" readonly="1"/>
                                        <field name="state" readonly="1"/>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Your Instances -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Instances Của Bạn</h4>
                                        <button name="action_request_new_instance" string="Yêu Cầu Instance Mới" type="object" class="btn btn-primary float-right"/>
                                    </div>
                                    <div class="card-body">
                                        <field name="instance_ids" readonly="1">
                                            <list string="Instances Của Bạn">
                                                <field name="name"/>
                                                <field name="subdomain"/>
                                                <field name="url" widget="url"/>
                                                <field name="state"/>
                                                <field name="created_date"/>
                                                <field name="expiry_date"/>
                                            </list>
                                        </field>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Support Section -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Hỗ Trợ</h4>
                                    </div>
                                    <div class="card-body">
                                        <p>Cần trợ giúp? Liên hệ đội ngũ hỗ trợ của chúng tôi:</p>
                                        <button name="action_contact_support" string="Liên Hệ Hỗ Trợ" type="object" class="btn btn-info"/>
                                        <button name="action_view_documentation" string="Xem Tài Liệu" type="object" class="btn btn-secondary"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Thanh Toán</h4>
                                    </div>
                                    <div class="card-body">
                                        <p>Quản lý đăng ký và thanh toán của bạn:</p>
                                        <button name="action_view_billing" string="Xem Thanh Toán" type="object" class="btn btn-success"/>
                                        <button name="action_upgrade_plan" string="Nâng Cấp Gói" type="object" class="btn btn-warning"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Customer Portal Action -->
    <record id="action_saas_customer_portal" model="ir.actions.act_window">
        <field name="name">Cổng Khách Hàng</field>
        <field name="res_model">saas.client</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_saas_customer_portal"/>
        <field name="target">current</field>
        <field name="context">{'portal_mode': True}</field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_saas_dashboard"
              name="Dashboard"
              parent="menu_saas_master_root"
              action="action_saas_dashboard"
              sequence="10"/>

    <menuitem id="menu_saas_customer_portal"
              name="Cổng Khách Hàng"
              parent="menu_saas_master_root"
              action="action_saas_customer_portal"
              sequence="60"/>
</odoo>
