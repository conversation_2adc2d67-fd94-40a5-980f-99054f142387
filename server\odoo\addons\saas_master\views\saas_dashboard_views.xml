<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- SaaS Dashboard View -->
    <record id="view_saas_dashboard" model="ir.ui.view">
        <field name="name">saas.dashboard</field>
        <field name="model">saas.client</field>
        <field name="arch" type="xml">
            <form string="SaaS Dashboard" create="false" edit="false" delete="false">
                <sheet>
                    <div class="o_dashboard">
                        <h1>SaaS Master Dashboard</h1>
                        
                        <!-- Key Metrics Row -->
                        <div class="row">
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h4>Total Clients</h4>
                                        <h2 id="total_clients">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h4>Active Instances</h4>
                                        <h2 id="active_instances">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <h4>Available Plans</h4>
                                        <h2 id="total_plans">0</h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <h4>Active Servers</h4>
                                        <h2 id="active_servers">0</h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Quick Actions Row -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h3>Quick Actions</h3>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <button name="action_new_client" string="New Client" type="object" class="btn btn-primary btn-lg btn-block"/>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <button name="action_new_instance" string="New Instance" type="object" class="btn btn-success btn-lg btn-block"/>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <button name="action_view_all_clients" string="View All Clients" type="object" class="btn btn-info btn-lg btn-block"/>
                            </div>
                            <div class="col-lg-3 col-md-6 col-sm-12">
                                <button name="action_view_all_instances" string="View All Instances" type="object" class="btn btn-warning btn-lg btn-block"/>
                            </div>
                        </div>
                        
                        <!-- Recent Activity Row -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Recent Clients</h4>
                                    </div>
                                    <div class="card-body">
                                        <div id="recent_clients">
                                            <!-- Recent clients will be loaded here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Recent Instances</h4>
                                    </div>
                                    <div class="card-body">
                                        <div id="recent_instances">
                                            <!-- Recent instances will be loaded here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Server Status Row -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Server Status</h4>
                                    </div>
                                    <div class="card-body">
                                        <div id="server_status">
                                            <!-- Server status will be loaded here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Dashboard Action -->
    <record id="action_saas_dashboard" model="ir.actions.act_window">
        <field name="name">SaaS Dashboard</field>
        <field name="res_model">saas.client</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_saas_dashboard"/>
        <field name="target">current</field>
        <field name="context">{'dashboard_mode': True}</field>
    </record>

    <!-- Customer Portal View -->
    <record id="view_saas_customer_portal" model="ir.ui.view">
        <field name="name">saas.customer.portal</field>
        <field name="model">saas.client</field>
        <field name="arch" type="xml">
            <form string="Customer Portal" create="false" edit="false" delete="false">
                <sheet>
                    <div class="o_customer_portal">
                        <h1>Welcome to Your SaaS Portal</h1>
                        
                        <!-- Customer Info -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Your Account Information</h4>
                                    </div>
                                    <div class="card-body">
                                        <field name="name" readonly="1"/>
                                        <field name="email" readonly="1"/>
                                        <field name="phone" readonly="1"/>
                                        <field name="plan_id" readonly="1"/>
                                        <field name="state" readonly="1"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Your Instances -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Your Instances</h4>
                                        <button name="action_request_new_instance" string="Request New Instance" type="object" class="btn btn-primary float-right"/>
                                    </div>
                                    <div class="card-body">
                                        <field name="instance_ids" readonly="1">
                                            <list string="Your Instances">
                                                <field name="name"/>
                                                <field name="subdomain"/>
                                                <field name="url" widget="url"/>
                                                <field name="state"/>
                                                <field name="created_date"/>
                                                <field name="expiry_date"/>
                                            </list>
                                        </field>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Support Section -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Support</h4>
                                    </div>
                                    <div class="card-body">
                                        <p>Need help? Contact our support team:</p>
                                        <button name="action_contact_support" string="Contact Support" type="object" class="btn btn-info"/>
                                        <button name="action_view_documentation" string="View Documentation" type="object" class="btn btn-secondary"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h4>Billing</h4>
                                    </div>
                                    <div class="card-body">
                                        <p>Manage your subscription and billing:</p>
                                        <button name="action_view_billing" string="View Billing" type="object" class="btn btn-success"/>
                                        <button name="action_upgrade_plan" string="Upgrade Plan" type="object" class="btn btn-warning"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Customer Portal Action -->
    <record id="action_saas_customer_portal" model="ir.actions.act_window">
        <field name="name">Customer Portal</field>
        <field name="res_model">saas.client</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_saas_customer_portal"/>
        <field name="target">current</field>
        <field name="context">{'portal_mode': True}</field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_saas_dashboard"
              name="Dashboard"
              parent="menu_saas_master_root"
              action="action_saas_dashboard"
              sequence="10"/>

    <menuitem id="menu_saas_customer_portal"
              name="Customer Portal"
              parent="menu_saas_master_root"
              action="action_saas_customer_portal"
              sequence="60"/>
</odoo>
