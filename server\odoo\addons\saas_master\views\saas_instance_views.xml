<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- SaaS Instance List View -->
    <record id="view_saas_instance_list" model="ir.ui.view">
        <field name="name">saas.instance.list</field>
        <field name="model">saas.instance</field>
        <field name="arch" type="xml">
            <list string="Instances SaaS" default_order="name">
                <field name="name"/>
                <field name="subdomain"/>
                <field name="client_id"/>
                <field name="plan_id"/>
                <field name="server_id"/>
                <field name="users_count" string="Người Dùng"/>
                <field name="storage_used_gb" string="Dung Lượng (GB)"/>
                <field name="state" widget="badge"
                       decoration-success="state == 'active'"
                       decoration-warning="state in ['suspended', 'maintenance']"
                       decoration-danger="state == 'terminated'"
                       decoration-info="state in ['draft', 'creating']"/>
                <field name="expiry_date"/>
            </list>
        </field>
    </record>

    <!-- SaaS Instance Form View -->
    <record id="view_saas_instance_form" model="ir.ui.view">
        <field name="name">saas.instance.form</field>
        <field name="model">saas.instance</field>
        <field name="arch" type="xml">
            <form string="Instance SaaS" class="o_form_view_sidebar_optimized">
                <header class="o_form_header_horizontal">
                    <button name="action_create_instance" string="Tạo Instance" type="object"
                            class="btn-primary" invisible="state != 'draft'"/>
                    <button name="action_activate" string="Kích Hoạt" type="object"
                            class="btn-success" invisible="state == 'active'"/>
                    <button name="action_suspend" string="Tạm Dừng" type="object"
                            class="btn-warning" invisible="state != 'active'"/>
                    <button name="action_maintenance" string="Bảo Trì" type="object"
                            class="btn-info" invisible="state == 'maintenance'"/>
                    <button name="action_terminate" string="Kết Thúc" type="object"
                            class="btn-danger" invisible="state == 'terminated'"
                            confirm="Bạn có chắc chắn muốn kết thúc instance này? Hành động này không thể hoàn tác."/>
                    <button name="action_backup" string="Sao Lưu Ngay" type="object"
                            class="btn-secondary" invisible="state not in ['active', 'suspended']"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,creating,active,suspended,maintenance,terminated"/>
                </header>
                <sheet class="o_form_sheet_sidebar_responsive">
                    <div class="oe_button_box" name="button_box">
                        <button name="%(action_saas_client)d" type="action" class="oe_stat_button" icon="fa-user"
                                context="{'search_default_id': client_id}">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Khách Hàng</span>
                            </div>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="name" placeholder="Tên Instance"/>
                            <field name="subdomain" placeholder="tên miền phụ"/>
                            <field name="database_name"/>
                            <field name="url" widget="url" readonly="1"/>
                        </group>
                        <group>
                            <field name="client_id"/>
                            <field name="plan_id"/>
                            <field name="server_id"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Chi Tiết Instance">
                            <group>
                                <group string="Truy Cập">
                                    <field name="admin_login"/>
                                    <field name="admin_password" password="True"/>
                                    <field name="custom_domain"/>
                                </group>
                                <group string="Cấu Hình">
                                    <field name="odoo_version"/>
                                    <field name="ssl_enabled"/>
                                    <field name="backup_enabled"/>
                                </group>
                            </group>
                            <group>
                                <field name="modules" placeholder="Danh sách modules đã cài đặt..."/>
                            </group>
                        </page>
                        <page string="Sử Dụng Tài Nguyên">
                            <group>
                                <group string="Sử Dụng Hiện Tại">
                                    <field name="users_count"/>
                                    <field name="storage_used_gb"/>
                                    <field name="storage_usage_percent" widget="percentage"/>
                                </group>
                                <group string="Giới Hạn Gói">
                                    <field name="plan_id" readonly="1" invisible="1"/>
                                </group>
                            </group>
                        </page>
                        <page string="Ngày Tháng &amp; Sao Lưu">
                            <group>
                                <group>
                                    <field name="created_date" readonly="1"/>
                                    <field name="expiry_date"/>
                                    <field name="days_until_expiry" readonly="1"/>
                                </group>
                                <group>
                                    <field name="last_backup_date" readonly="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- SaaS Instance Kanban View -->
    <record id="view_saas_instance_kanban" model="ir.ui.view">
        <field name="name">saas.instance.kanban</field>
        <field name="model">saas.instance</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile o_kanban_sidebar_responsive">
                <field name="name"/>
                <field name="subdomain"/>
                <field name="client_id"/>
                <field name="state"/>
                <field name="url"/>
                <field name="storage_usage_percent"/>
                <templates>
                    <t t-name="card">
                        <div class="oe_kanban_content">
                            <div class="row">
                                <div class="col-8">
                                    <strong><field name="name"/></strong>
                                </div>
                                <div class="col-4 text-end">
                                    <field name="state" widget="badge"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span class="text-muted">Khách Hàng: </span><field name="client_id"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span class="text-muted">URL: </span>
                                    <a t-att-href="record.url.raw_value" target="_blank">
                                        <field name="subdomain"/>
                                    </a>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span class="text-muted">Dung Lượng: </span>
                                    <field name="storage_usage_percent" widget="percentage"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- SaaS Instance Search View -->
    <record id="view_saas_instance_search" model="ir.ui.view">
        <field name="name">saas.instance.search</field>
        <field name="model">saas.instance</field>
        <field name="arch" type="xml">
            <search string="Tìm Kiếm Instances SaaS">
                <field name="name" string="Tên Instance" filter_domain="['|', ('name', 'ilike', self), ('subdomain', 'ilike', self)]"/>
                <field name="subdomain" string="Tên Miền Phụ"/>
                <field name="database_name" string="Tên Database"/>
                <field name="client_id" string="Khách Hàng"/>
                <field name="plan_id" string="Gói Dịch Vụ"/>
                <field name="server_id" string="Server"/>
                <separator/>
                <filter string="Đang Hoạt Động" name="active" domain="[('active', '=', True)]" help="Instances đang hoạt động"/>
                <filter string="Không Hoạt Động" name="inactive" domain="[('active', '=', False)]" help="Instances không hoạt động"/>
                <separator/>
                <filter string="Nháp" name="draft" domain="[('state', '=', 'draft')]" help="Instances ở trạng thái nháp"/>
                <filter string="Instances Hoạt Động" name="active_instances" domain="[('state', '=', 'active')]" help="Instances đang hoạt động"/>
                <filter string="Tạm Dừng" name="suspended" domain="[('state', '=', 'suspended')]" help="Instances bị tạm dừng"/>
                <filter string="Bảo Trì" name="maintenance" domain="[('state', '=', 'maintenance')]" help="Instances đang bảo trì"/>
                <filter string="Đã Kết Thúc" name="terminated" domain="[('state', '=', 'terminated')]" help="Instances đã kết thúc"/>
                <separator/>
                <filter string="Có Ngày Hết Hạn" name="has_expiry" domain="[('expiry_date', '!=', False)]" help="Instances có ngày hết hạn"/>
                <group expand="0" string="Nhóm Theo">
                    <filter string="Khách Hàng" name="group_client" context="{'group_by': 'client_id'}"/>
                    <filter string="Gói Dịch Vụ" name="group_plan" context="{'group_by': 'plan_id'}"/>
                    <filter string="Server" name="group_server" context="{'group_by': 'server_id'}"/>
                    <filter string="Trạng Thái" name="group_state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- SaaS Instance Action -->
    <record id="action_saas_instance" model="ir.actions.act_window">
        <field name="name">Instances SaaS</field>
        <field name="res_model">saas.instance</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="search_view_id" ref="view_saas_instance_search"/>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Tạo instance SaaS đầu tiên của bạn!
            </p>
            <p>
                Instances là các môi trường database riêng biệt cho khách hàng của bạn.
                Mỗi instance chạy trên một server và tuân theo các giới hạn được định nghĩa bởi gói dịch vụ của nó.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_saas_instance"
              name="Instances"
              parent="menu_saas_master_root"
              action="action_saas_instance"
              sequence="30"/>
</odoo>
