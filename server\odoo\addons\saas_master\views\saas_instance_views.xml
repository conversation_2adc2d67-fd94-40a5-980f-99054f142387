<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- SaaS Instance List View -->
    <record id="view_saas_instance_list" model="ir.ui.view">
        <field name="name">saas.instance.list</field>
        <field name="model">saas.instance</field>
        <field name="arch" type="xml">
            <list string="SaaS Instances">
                <field name="name"/>
                <field name="subdomain"/>
                <field name="client_id"/>
                <field name="plan_id"/>
                <field name="server_id"/>
                <field name="users_count"/>
                <field name="storage_used_gb"/>
                <field name="state" widget="badge" 
                       decoration-success="state == 'active'"
                       decoration-warning="state in ['suspended', 'maintenance']"
                       decoration-danger="state == 'terminated'"
                       decoration-info="state in ['draft', 'creating']"/>
                <field name="expiry_date"/>
            </list>
        </field>
    </record>

    <!-- SaaS Instance Form View -->
    <record id="view_saas_instance_form" model="ir.ui.view">
        <field name="name">saas.instance.form</field>
        <field name="model">saas.instance</field>
        <field name="arch" type="xml">
            <form string="SaaS Instance">
                <header>
                    <button name="action_create_instance" string="Create Instance" type="object" 
                            class="btn-primary" invisible="state != 'draft'"/>
                    <button name="action_activate" string="Activate" type="object" 
                            class="btn-success" invisible="state == 'active'"/>
                    <button name="action_suspend" string="Suspend" type="object" 
                            class="btn-warning" invisible="state != 'active'"/>
                    <button name="action_maintenance" string="Maintenance" type="object" 
                            class="btn-info" invisible="state == 'maintenance'"/>
                    <button name="action_terminate" string="Terminate" type="object" 
                            class="btn-danger" invisible="state == 'terminated'"
                            confirm="Are you sure you want to terminate this instance? This action cannot be undone."/>
                    <button name="action_backup" string="Backup Now" type="object" 
                            class="btn-secondary" invisible="state not in ['active', 'suspended']"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,creating,active,suspended,maintenance,terminated"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="%(action_saas_client)d" type="action" class="oe_stat_button" icon="fa-user"
                                context="{'search_default_id': client_id}">
                            <div class="o_field_widget o_stat_info">
                                <span class="o_stat_text">Client</span>
                            </div>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="name" placeholder="Instance Name"/>
                            <field name="subdomain" placeholder="subdomain"/>
                            <field name="database_name"/>
                            <field name="url" widget="url" readonly="1"/>
                        </group>
                        <group>
                            <field name="client_id"/>
                            <field name="plan_id"/>
                            <field name="server_id"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Instance Details">
                            <group>
                                <group string="Access">
                                    <field name="admin_login"/>
                                    <field name="admin_password" password="True"/>
                                    <field name="custom_domain"/>
                                </group>
                                <group string="Configuration">
                                    <field name="odoo_version"/>
                                    <field name="ssl_enabled"/>
                                    <field name="backup_enabled"/>
                                </group>
                            </group>
                            <group>
                                <field name="modules" placeholder="List of installed modules..."/>
                            </group>
                        </page>
                        <page string="Resource Usage">
                            <group>
                                <group string="Current Usage">
                                    <field name="users_count"/>
                                    <field name="storage_used_gb"/>
                                    <field name="storage_usage_percent" widget="percentage"/>
                                </group>
                                <group string="Plan Limits">
                                    <field name="plan_id" readonly="1" invisible="1"/>
                                </group>
                            </group>
                        </page>
                        <page string="Dates &amp; Backup">
                            <group>
                                <group>
                                    <field name="created_date" readonly="1"/>
                                    <field name="expiry_date"/>
                                    <field name="days_until_expiry" readonly="1"/>
                                </group>
                                <group>
                                    <field name="last_backup_date" readonly="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- SaaS Instance Kanban View -->
    <record id="view_saas_instance_kanban" model="ir.ui.view">
        <field name="name">saas.instance.kanban</field>
        <field name="model">saas.instance</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <field name="name"/>
                <field name="subdomain"/>
                <field name="client_id"/>
                <field name="state"/>
                <field name="url"/>
                <field name="storage_usage_percent"/>
                <templates>
                    <t t-name="card">
                        <div class="oe_kanban_content">
                            <div class="row">
                                <div class="col-8">
                                    <strong><field name="name"/></strong>
                                </div>
                                <div class="col-4 text-end">
                                    <field name="state" widget="badge"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span class="text-muted">Client: </span><field name="client_id"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span class="text-muted">URL: </span>
                                    <a t-att-href="record.url.raw_value" target="_blank">
                                        <field name="subdomain"/>
                                    </a>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span class="text-muted">Storage: </span>
                                    <field name="storage_usage_percent" widget="percentage"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- SaaS Instance Search View -->
    <record id="view_saas_instance_search" model="ir.ui.view">
        <field name="name">saas.instance.search</field>
        <field name="model">saas.instance</field>
        <field name="arch" type="xml">
            <search string="Search SaaS Instances">
                <field name="name" string="Instance Name"/>
                <field name="subdomain"/>
                <field name="database_name"/>
                <field name="client_id"/>
                <field name="plan_id"/>
                <field name="server_id"/>
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Active Instances" name="active_instances" domain="[('state', '=', 'active')]"/>
                <filter string="Suspended" name="suspended" domain="[('state', '=', 'suspended')]"/>
                <filter string="Maintenance" name="maintenance" domain="[('state', '=', 'maintenance')]"/>
                <filter string="Terminated" name="terminated" domain="[('state', '=', 'terminated')]"/>
                <separator/>
                <filter string="Has Expiry Date" name="has_expiry" domain="[('expiry_date', '!=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Client" name="group_client" context="{'group_by': 'client_id'}"/>
                    <filter string="Plan" name="group_plan" context="{'group_by': 'plan_id'}"/>
                    <filter string="Server" name="group_server" context="{'group_by': 'server_id'}"/>
                    <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- SaaS Instance Action -->
    <record id="action_saas_instance" model="ir.actions.act_window">
        <field name="name">SaaS Instances</field>
        <field name="res_model">saas.instance</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="search_view_id" ref="view_saas_instance_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first SaaS instance!
            </p>
            <p>
                Instances are individual database environments for your clients.
                Each instance runs on a server and follows the limits defined by its service plan.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_saas_instance"
              name="Instances"
              parent="menu_saas_master_root"
              action="action_saas_instance"
              sequence="30"/>
</odoo>
