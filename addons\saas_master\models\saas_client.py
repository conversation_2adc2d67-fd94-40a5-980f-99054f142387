# -*- coding: utf-8 -*-

from odoo import models, fields, api


class SaasClient(models.Model):
    _name = 'saas.client'
    _description = 'SaaS Client'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(
        string='Client Name',
        required=True,
        tracking=True,
        help='Name of the SaaS client'
    )
    
    email = fields.Char(
        string='Email',
        required=True,
        tracking=True,
        help='Primary email address for the client'
    )
    
    phone = fields.Char(
        string='Phone',
        tracking=True,
        help='Contact phone number'
    )
    
    company_name = fields.Char(
        string='Company Name',
        tracking=True,
        help='Name of the client company'
    )
    
    state = fields.Selection([
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('cancelled', 'Cancelled'),
    ], string='Status', default='draft', required=True, tracking=True)
    
    partner_id = fields.Many2one(
        'res.partner',
        string='Related Partner',
        help='Related partner record for billing and contact management'
    )
    
    notes = fields.Text(
        string='Notes',
        help='Additional notes about the client'
    )
    
    active = fields.Boolean(
        string='Active',
        default=True,
        help='Uncheck to archive the client'
    )
    
    @api.model
    def create(self, vals):
        """Create related partner when creating client"""
        if not vals.get('partner_id') and vals.get('name'):
            partner_vals = {
                'name': vals.get('company_name') or vals.get('name'),
                'email': vals.get('email'),
                'phone': vals.get('phone'),
                'is_company': bool(vals.get('company_name')),
                'customer_rank': 1,
            }
            partner = self.env['res.partner'].create(partner_vals)
            vals['partner_id'] = partner.id
        return super().create(vals)
    
    def write(self, vals):
        """Update related partner when updating client"""
        result = super().write(vals)
        if self.partner_id and any(field in vals for field in ['name', 'email', 'phone', 'company_name']):
            partner_vals = {}
            if 'name' in vals or 'company_name' in vals:
                partner_vals['name'] = vals.get('company_name') or vals.get('name') or self.company_name or self.name
            if 'email' in vals:
                partner_vals['email'] = vals['email']
            if 'phone' in vals:
                partner_vals['phone'] = vals['phone']
            if partner_vals:
                self.partner_id.write(partner_vals)
        return result
    
    def action_activate(self):
        """Activate the client"""
        self.write({'state': 'active'})
    
    def action_suspend(self):
        """Suspend the client"""
        self.write({'state': 'suspended'})
    
    def action_cancel(self):
        """Cancel the client"""
        self.write({'state': 'cancelled'})
    
    def action_reset_to_draft(self):
        """Reset client to draft state"""
        self.write({'state': 'draft'})
