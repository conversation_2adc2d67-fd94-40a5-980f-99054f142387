<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- SaaS Plan List View -->
    <record id="view_saas_plan_list" model="ir.ui.view">
        <field name="name">saas.plan.list</field>
        <field name="model">saas.plan</field>
        <field name="arch" type="xml">
            <list string="SaaS Plans">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="code"/>
                <field name="price" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                <field name="currency_id" column_invisible="1"/>
                <field name="billing_cycle"/>
                <field name="max_users"/>
                <field name="client_count"/>
                <field name="state" widget="badge" 
                       decoration-success="state == 'active'"
                       decoration-warning="state == 'deprecated'"
                       decoration-danger="state == 'archived'"
                       decoration-info="state == 'draft'"/>
            </list>
        </field>
    </record>

    <!-- SaaS Plan Form View -->
    <record id="view_saas_plan_form" model="ir.ui.view">
        <field name="name">saas.plan.form</field>
        <field name="model">saas.plan</field>
        <field name="arch" type="xml">
            <form string="SaaS Plan">
                <header>
                    <button name="action_activate" string="Activate" type="object" 
                            class="btn-primary" invisible="state == 'active'"/>
                    <button name="action_deprecate" string="Deprecate" type="object" 
                            class="btn-warning" invisible="state != 'active'"/>
                    <button name="action_archive_plan" string="Archive" type="object" 
                            class="btn-danger" invisible="state == 'archived'"/>
                    <button name="action_reset_to_draft" string="Reset to Draft" type="object" 
                            class="btn-secondary" invisible="state == 'draft'"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,active,deprecated,archived"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_clients" type="object" class="oe_stat_button" icon="fa-users">
                            <field name="client_count" widget="statinfo" string="Clients"/>
                        </button>
                        <button name="action_view_instances" type="object" class="oe_stat_button" icon="fa-database">
                            <field name="instance_count" widget="statinfo" string="Instances"/>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="name" placeholder="Plan Name"/>
                            <field name="code" placeholder="PLAN_CODE"/>
                            <field name="sequence"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="price" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                            <field name="currency_id"/>
                            <field name="billing_cycle"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Plan Details">
                            <group>
                                <group string="Limits">
                                    <field name="max_users"/>
                                    <field name="max_storage_gb"/>
                                    <field name="max_databases"/>
                                </group>
                                <group string="Features">
                                    <field name="has_custom_domain"/>
                                    <field name="has_ssl"/>
                                    <field name="has_backup"/>
                                    <field name="has_support"/>
                                    <field name="support_level" invisible="not has_support"/>
                                </group>
                            </group>
                            <group>
                                <field name="description" placeholder="Detailed description of the plan..."/>
                                <field name="features" placeholder="List of features included in this plan..."/>
                            </group>
                        </page>
                        <page string="Technical">
                            <group>
                                <field name="template_db"/>
                                <field name="server_id"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- SaaS Plan Kanban View -->
    <record id="view_saas_plan_kanban" model="ir.ui.view">
        <field name="name">saas.plan.kanban</field>
        <field name="model">saas.plan</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <field name="name"/>
                <field name="code"/>
                <field name="price"/>
                <field name="currency_id"/>
                <field name="billing_cycle"/>
                <field name="state"/>
                <field name="client_count"/>
                <templates>
                    <t t-name="card">
                        <div class="oe_kanban_content">
                            <div class="row">
                                <div class="col-6">
                                    <strong><field name="name"/></strong>
                                </div>
                                <div class="col-6 text-end">
                                    <field name="state" widget="badge"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span class="text-muted">Code: </span><field name="code"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span class="text-muted">Price: </span>
                                    <field name="price" widget="monetary" options="{'currency_field': 'currency_id'}"/>
                                    <span class="text-muted"> / </span><field name="billing_cycle"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span class="text-muted">Clients: </span><field name="client_count"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- SaaS Plan Search View -->
    <record id="view_saas_plan_search" model="ir.ui.view">
        <field name="name">saas.plan.search</field>
        <field name="model">saas.plan</field>
        <field name="arch" type="xml">
            <search string="Search SaaS Plans">
                <field name="name" string="Plan Name"/>
                <field name="code"/>
                <field name="description"/>
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Active Plans" name="active_plans" domain="[('state', '=', 'active')]"/>
                <filter string="Deprecated" name="deprecated" domain="[('state', '=', 'deprecated')]"/>
                <filter string="Archived" name="archived" domain="[('state', '=', 'archived')]"/>
                <separator/>
                <filter string="Monthly" name="monthly" domain="[('billing_cycle', '=', 'monthly')]"/>
                <filter string="Yearly" name="yearly" domain="[('billing_cycle', '=', 'yearly')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Billing Cycle" name="group_billing" context="{'group_by': 'billing_cycle'}"/>
                    <filter string="Support Level" name="group_support" context="{'group_by': 'support_level'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- SaaS Plan Action -->
    <record id="action_saas_plan" model="ir.actions.act_window">
        <field name="name">SaaS Plans</field>
        <field name="res_model">saas.plan</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="search_view_id" ref="view_saas_plan_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first SaaS plan!
            </p>
            <p>
                Define service plans with different features, pricing, and limits.
                Plans help you organize your SaaS offerings and manage client subscriptions.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_saas_plan"
              name="Plans"
              parent="menu_saas_master_root"
              action="action_saas_plan"
              sequence="20"/>
</odoo>
