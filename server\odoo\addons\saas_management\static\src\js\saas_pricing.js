/** @odoo-module **/

import { Component, useState, onMounted } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";

/**
 * SaaS Pricing Component
 * Handles pricing display, plan selection, and subscription management
 */
class SaasPricingComponent extends Component {
    setup() {
        this.rpc = useService("rpc");
        this.notification = useService("notification");
        this.state = useState({
            plans: [],
            loading: true,
            selectedPlan: null,
            billingCycle: 'monthly', // monthly, yearly
        });

        onMounted(async () => {
            await this.loadPricingPlans();
        });
    }

    /**
     * Load pricing plans from backend
     */
    async loadPricingPlans() {
        try {
            this.state.loading = true;
            const plans = await this.rpc("/saas/api/pricing/plans", {});
            this.state.plans = plans || this.getDefaultPlans();
        } catch (error) {
            console.error("Error loading pricing plans:", error);
            this.state.plans = this.getDefaultPlans();
        } finally {
            this.state.loading = false;
        }
    }

    /**
     * Get default pricing plans if backend is not available
     */
    getDefaultPlans() {
        return [
            {
                id: 1,
                name: "Starter",
                description: "Perfect for small businesses",
                monthly_price: 29,
                yearly_price: 290,
                features: [
                    "Up to 5 users",
                    "10GB storage",
                    "Basic support",
                    "Standard apps",
                    "SSL certificate"
                ],
                unavailable_features: [
                    "Custom domain",
                    "Advanced backup",
                    "Priority support"
                ],
                is_popular: false
            },
            {
                id: 2,
                name: "Professional",
                description: "Best for growing companies",
                monthly_price: 79,
                yearly_price: 790,
                features: [
                    "Up to 25 users",
                    "50GB storage",
                    "Priority support",
                    "All standard apps",
                    "Custom domain",
                    "SSL certificate",
                    "Daily backups"
                ],
                unavailable_features: [
                    "Advanced integrations",
                    "Custom development"
                ],
                is_popular: true
            },
            {
                id: 3,
                name: "Enterprise",
                description: "For large organizations",
                monthly_price: 199,
                yearly_price: 1990,
                features: [
                    "Unlimited users",
                    "500GB storage",
                    "24/7 premium support",
                    "All apps included",
                    "Multiple custom domains",
                    "SSL certificate",
                    "Hourly backups",
                    "Advanced integrations",
                    "Custom development"
                ],
                unavailable_features: [],
                is_popular: false
            }
        ];
    }

    /**
     * Handle billing cycle change
     */
    onBillingCycleChange(cycle) {
        this.state.billingCycle = cycle;
        this.updatePricingDisplay();
    }

    /**
     * Update pricing display based on billing cycle
     */
    updatePricingDisplay() {
        const priceElements = document.querySelectorAll('.saas-plan-price');
        const periodElements = document.querySelectorAll('.saas-plan-period');
        
        priceElements.forEach((element, index) => {
            const plan = this.state.plans[index];
            if (plan) {
                const price = this.state.billingCycle === 'yearly' 
                    ? plan.yearly_price 
                    : plan.monthly_price;
                element.textContent = `$${price}`;
            }
        });

        periodElements.forEach(element => {
            element.textContent = this.state.billingCycle === 'yearly' 
                ? '/year' 
                : '/month';
        });
    }

    /**
     * Handle plan selection
     */
    async selectPlan(planId) {
        try {
            this.state.selectedPlan = planId;
            
            // Show loading state
            const button = document.querySelector(`[data-plan-id="${planId}"] .saas-plan-button`);
            if (button) {
                button.innerHTML = '<span class="saas-loading"></span> Processing...';
                button.disabled = true;
            }

            // Redirect to subscription flow
            await this.redirectToSubscription(planId);
            
        } catch (error) {
            console.error("Error selecting plan:", error);
            this.notification.add("Error selecting plan. Please try again.", {
                type: "danger"
            });
        }
    }

    /**
     * Redirect to subscription flow
     */
    async redirectToSubscription(planId) {
        const plan = this.state.plans.find(p => p.id === planId);
        const billingCycle = this.state.billingCycle;
        
        // Create subscription URL with parameters
        const params = new URLSearchParams({
            plan_id: planId,
            billing_cycle: billingCycle,
            price: billingCycle === 'yearly' ? plan.yearly_price : plan.monthly_price
        });

        // Redirect to subscription page
        window.location.href = `/saas/subscribe?${params.toString()}`;
    }

    /**
     * Calculate yearly savings
     */
    calculateYearlySavings(plan) {
        const monthlyTotal = plan.monthly_price * 12;
        const savings = monthlyTotal - plan.yearly_price;
        const percentage = Math.round((savings / monthlyTotal) * 100);
        return { amount: savings, percentage };
    }

    /**
     * Format price display
     */
    formatPrice(price) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0
        }).format(price);
    }

    /**
     * Render pricing cards
     */
    renderPricingCards() {
        if (this.state.loading) {
            return '<div class="saas-loading-overlay"><div class="saas-loading"></div></div>';
        }

        return this.state.plans.map(plan => {
            const price = this.state.billingCycle === 'yearly' 
                ? plan.yearly_price 
                : plan.monthly_price;
            
            const savings = this.calculateYearlySavings(plan);
            
            return `
                <div class="saas-pricing-card ${plan.is_popular ? 'featured' : ''}" data-plan-id="${plan.id}">
                    <h3 class="saas-plan-name">${plan.name}</h3>
                    <div class="saas-plan-price">${this.formatPrice(price)}</div>
                    <div class="saas-plan-period">/${this.state.billingCycle === 'yearly' ? 'year' : 'month'}</div>
                    
                    ${this.state.billingCycle === 'yearly' && savings.percentage > 0 ? 
                        `<div class="saas-savings-badge">Save ${savings.percentage}%</div>` : ''
                    }
                    
                    <p class="saas-plan-description">${plan.description}</p>
                    
                    <ul class="saas-plan-features">
                        ${plan.features.map(feature => 
                            `<li>${feature}</li>`
                        ).join('')}
                        ${plan.unavailable_features.map(feature => 
                            `<li class="unavailable">${feature}</li>`
                        ).join('')}
                    </ul>
                    
                    <button class="saas-plan-button" onclick="saasApp.selectPlan(${plan.id})">
                        Choose ${plan.name}
                    </button>
                </div>
            `;
        }).join('');
    }
}

/**
 * SaaS Pricing Application
 * Main application class for pricing functionality
 */
class SaasPricingApp {
    constructor() {
        this.component = null;
        this.init();
    }

    async init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeApp());
        } else {
            this.initializeApp();
        }
    }

    initializeApp() {
        // Initialize pricing component if pricing section exists
        const pricingSection = document.querySelector('.saas-pricing-section');
        if (pricingSection) {
            this.component = new SaasPricingComponent();
            this.renderPricingSection();
            this.attachEventListeners();
        }
    }

    renderPricingSection() {
        const container = document.querySelector('.saas-pricing-grid');
        if (container && this.component) {
            container.innerHTML = this.component.renderPricingCards();
        }
    }

    attachEventListeners() {
        // Billing cycle toggle
        const billingToggles = document.querySelectorAll('.saas-billing-toggle');
        billingToggles.forEach(toggle => {
            toggle.addEventListener('change', (e) => {
                const cycle = e.target.value;
                this.component.onBillingCycleChange(cycle);
            });
        });

        // Plan selection buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('saas-plan-button')) {
                const planCard = e.target.closest('.saas-pricing-card');
                const planId = parseInt(planCard.dataset.planId);
                this.selectPlan(planId);
            }
        });
    }

    async selectPlan(planId) {
        if (this.component) {
            await this.component.selectPlan(planId);
        }
    }
}

// Initialize the application
const saasApp = new SaasPricingApp();

// Export for global access
window.saasApp = saasApp;

// Register component for Odoo
registry.category("public_components").add("saas_pricing", SaasPricingComponent);
