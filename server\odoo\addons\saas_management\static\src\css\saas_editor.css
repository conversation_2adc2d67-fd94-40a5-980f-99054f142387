/* SaaS Management Website Editor Styles */

/* ===== EDITOR PANEL STYLES ===== */
.saas-editor-panel {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.saas-editor-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #eee;
}

.saas-editor-section {
    margin-bottom: 25px;
}

.saas-editor-label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.saas-editor-input {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
}

.saas-editor-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.saas-editor-select {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.95rem;
    background: white;
    transition: border-color 0.3s ease;
}

.saas-editor-textarea {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.95rem;
    min-height: 80px;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.saas-editor-checkbox {
    margin-right: 8px;
}

.saas-editor-checkbox-label {
    display: flex;
    align-items: center;
    font-weight: normal;
    cursor: pointer;
}

.saas-editor-help {
    font-size: 0.85rem;
    color: #666;
    margin-top: 5px;
    font-style: italic;
}

/* ===== COLOR PICKER ===== */
.saas-color-picker {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 10px;
}

.saas-color-option {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 3px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.saas-color-option:hover {
    transform: scale(1.1);
    border-color: #333;
}

.saas-color-option.selected {
    border-color: #667eea;
    transform: scale(1.15);
}

.saas-color-option.selected::after {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: bold;
    text-shadow: 0 0 3px rgba(0,0,0,0.5);
}

/* ===== PRICING EDITOR ===== */
.saas-pricing-editor {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.saas-plan-editor {
    background: white;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #667eea;
}

.saas-plan-editor.featured {
    border-left-color: #ffd700;
    background: #fffbf0;
}

.saas-plan-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 15px;
}

.saas-plan-name-input {
    font-size: 1.1rem;
    font-weight: 600;
    border: none;
    background: transparent;
    color: #333;
    flex: 1;
}

.saas-plan-featured-toggle {
    margin-left: 15px;
}

.saas-price-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.saas-features-editor {
    margin-bottom: 15px;
}

.saas-feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.saas-feature-input {
    flex: 1;
    margin-right: 10px;
}

.saas-feature-remove {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
    font-size: 0.8rem;
}

.saas-feature-add {
    background: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 15px;
    cursor: pointer;
    font-size: 0.9rem;
    margin-top: 10px;
}

/* ===== LAYOUT EDITOR ===== */
.saas-layout-editor {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.saas-layout-preview {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    min-height: 300px;
    border: 2px dashed #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-style: italic;
}

.saas-layout-controls {
    background: white;
    border-radius: 8px;
    padding: 20px;
}

.saas-layout-option {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.saas-layout-option:hover {
    border-color: #667eea;
    background: #f8f9fa;
}

.saas-layout-option.selected {
    border-color: #667eea;
    background: #e7f3ff;
}

.saas-layout-option input[type="radio"] {
    margin-right: 10px;
}

/* ===== BUTTON STYLES ===== */
.saas-editor-btn {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 0.95rem;
}

.saas-editor-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.saas-editor-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.saas-editor-btn-secondary {
    background: #6c757d;
    color: white;
}

.saas-editor-btn-success {
    background: #28a745;
    color: white;
}

.saas-editor-btn-danger {
    background: #dc3545;
    color: white;
}

.saas-editor-btn-small {
    padding: 6px 12px;
    font-size: 0.85rem;
}

/* ===== TABS ===== */
.saas-editor-tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
}

.saas-editor-tab {
    padding: 12px 20px;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 600;
    color: #666;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.saas-editor-tab:hover {
    color: #333;
    background: #f8f9fa;
}

.saas-editor-tab.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.saas-editor-tab-content {
    display: none;
}

.saas-editor-tab-content.active {
    display: block;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .saas-layout-editor {
        grid-template-columns: 1fr;
    }
    
    .saas-price-inputs {
        grid-template-columns: 1fr;
    }
    
    .saas-color-picker {
        justify-content: center;
    }
    
    .saas-editor-tabs {
        flex-wrap: wrap;
    }
    
    .saas-editor-tab {
        flex: 1;
        min-width: 120px;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.saas-editor-panel {
    animation: fadeIn 0.3s ease-out;
}

/* ===== DRAG AND DROP ===== */
.saas-draggable {
    cursor: move;
    transition: all 0.3s ease;
}

.saas-draggable:hover {
    transform: scale(1.02);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.saas-dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.saas-drop-zone {
    border: 2px dashed #667eea;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    color: #667eea;
    background: rgba(102, 126, 234, 0.05);
    transition: all 0.3s ease;
}

.saas-drop-zone.active {
    background: rgba(102, 126, 234, 0.1);
    border-color: #4c63d2;
}

/* ===== SAVE INDICATOR ===== */
.saas-save-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 10px 15px;
    border-radius: 6px;
    color: white;
    font-weight: 600;
    z-index: 1000;
    transition: all 0.3s ease;
}

.saas-save-indicator.saving {
    background: #ffc107;
}

.saas-save-indicator.saved {
    background: #28a745;
}

.saas-save-indicator.error {
    background: #dc3545;
}
