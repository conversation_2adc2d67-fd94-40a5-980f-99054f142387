<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Product Mapping Form View -->
    <record id="view_multichannel_product_mapping_form" model="ir.ui.view">
        <field name="name">multichannel.product.mapping.form</field>
        <field name="model">multichannel.product.mapping</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_sync_to_platform" string="Sync to Platform" type="object" class="btn-primary"/>
                    <button name="action_sync_from_platform" string="Sync from Platform" type="object" class="btn-secondary"/>
                    <field name="mapping_status" widget="statusbar"/>
                </header>
                <sheet>
                    <group>
                        <group string="Odoo Product">
                            <field name="shop_id"/>
                            <field name="product_id"/>
                            <field name="platform" readonly="1"/>
                        </group>
                        <group string="External Product">
                            <field name="external_product_id"/>
                            <field name="external_sku"/>
                            <field name="external_name"/>
                            <field name="external_url" widget="url"/>
                            <field name="external_image_url" widget="url"/>
                        </group>
                    </group>
                    <group>
                        <group string="Sync Configuration">
                            <field name="auto_sync"/>
                            <field name="sync_direction"/>
                            <field name="sync_price"/>
                            <field name="sync_inventory"/>
                            <field name="sync_description"/>
                            <field name="sync_images"/>
                        </group>
                        <group string="Status">
                            <field name="mapping_status"/>
                            <field name="last_sync_date" readonly="1"/>
                            <field name="last_sync_direction" readonly="1"/>
                            <field name="sync_status" readonly="1"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Variant Mappings">
                            <field name="variant_mapping_ids">
                                <tree editable="bottom">
                                    <field name="product_id"/>
                                    <field name="external_variant_id"/>
                                    <field name="external_sku"/>
                                    <field name="external_price"/>
                                    <field name="external_inventory_qty"/>
                                    <field name="variant_status"/>
                                    <field name="last_sync_date"/>
                                </tree>
                            </field>
                        </page>

                        <page string="TikTok Shop" attrs="{'invisible': [('platform', '!=', 'tiktok')]}">
                            <group>
                                <field name="tiktok_product_status"/>
                                <field name="tiktok_category_id"/>
                                <field name="tiktok_brand_id"/>
                            </group>
                        </page>

                        <page string="Shopee" attrs="{'invisible': [('platform', '!=', 'shopee')]}">
                            <group>
                                <field name="shopee_item_status"/>
                                <field name="shopee_category_id"/>
                            </group>
                        </page>

                        <page string="Lazada" attrs="{'invisible': [('platform', '!=', 'lazada')]}">
                            <group>
                                <field name="lazada_item_status"/>
                                <field name="lazada_category_id"/>
                            </group>
                        </page>

                        <page string="Statistics">
                            <group>
                                <field name="total_sales"/>
                                <field name="total_revenue"/>
                                <field name="currency_id"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Product Mapping List View -->
    <record id="view_multichannel_product_mapping_list" model="ir.ui.view">
        <field name="name">multichannel.product.mapping.list</field>
        <field name="model">multichannel.product.mapping</field>
        <field name="arch" type="xml">
            <list string="Product Mappings" decoration-success="mapping_status=='synced'" decoration-warning="mapping_status=='syncing'" decoration-danger="mapping_status=='error'">
                <field name="name"/>
                <field name="shop_id"/>
                <field name="platform"/>
                <field name="product_id"/>
                <field name="external_product_id"/>
                <field name="mapping_status" widget="badge"/>
                <field name="last_sync_date"/>
                <field name="total_sales"/>
                <field name="total_revenue"/>
                <button name="action_sync_to_platform" type="object" string="Sync to Platform" icon="fa-upload" attrs="{'invisible': [('mapping_status', 'in', ['syncing'])]}"/>
                <button name="action_sync_from_platform" type="object" string="Sync from Platform" icon="fa-download" attrs="{'invisible': [('mapping_status', 'in', ['syncing'])]}"/>
            </list>
        </field>
    </record>

    <!-- Product Mapping Action -->
    <record id="action_multichannel_product_mapping" model="ir.actions.act_window">
        <field name="name">Product Mappings</field>
        <field name="res_model">multichannel.product.mapping</field>
        <field name="view_mode">list,form</field>
    </record>
</odoo>
