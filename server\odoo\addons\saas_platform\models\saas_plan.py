from odoo import models, fields, api

class SaasPlan(models.Model):
    _name = 'saas.plan'
    _description = 'SaaS Subscription Plan'
    _order = 'sequence, name'
    
    # Basic Information
    name = fields.Char(
        string='Plan Name', 
        required=True,
        help='Name of the subscription plan'
    )
    
    description = fields.Text(
        string='Description',
        help='Detailed description of the plan features'
    )
    
    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help='Used to order plans'
    )
    
    # Pricing
    monthly_price = fields.Monetary(
        string='Monthly Price',
        currency_field='currency_id',
        help='Monthly subscription price'
    )
    
    yearly_price = fields.Monetary(
        string='Yearly Price',
        currency_field='currency_id',
        help='Yearly subscription price'
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id,
        required=True
    )
    
    # Limits and Features
    max_users = fields.Integer(
        string='Max Users',
        default=1,
        help='Maximum number of users allowed'
    )
    
    max_storage_gb = fields.Float(
        string='Max Storage (GB)',
        default=1.0,
        help='Maximum storage space in GB'
    )
    
    features = fields.Text(
        string='Features',
        help='List of features included in this plan'
    )
    
    # Status
    active = fields.Boolean(
        string='Active',
        default=True,
        help='Whether this plan is available for new subscriptions'
    )
    
    # Computed Fields
    yearly_discount = fields.Float(
        string='Yearly Discount (%)',
        compute='_compute_yearly_discount',
        store=True,
        help='Discount percentage when paying yearly vs monthly'
    )
    
    @api.depends('monthly_price', 'yearly_price')
    def _compute_yearly_discount(self):
        for record in self:
            if record.monthly_price and record.yearly_price:
                yearly_equivalent = record.monthly_price * 12
                if yearly_equivalent > 0:
                    discount = ((yearly_equivalent - record.yearly_price) / yearly_equivalent) * 100
                    record.yearly_discount = round(discount, 2)
                else:
                    record.yearly_discount = 0.0
            else:
                record.yearly_discount = 0.0
    
    # Display name
    def name_get(self):
        result = []
        for record in self:
            name = record.name
            if record.monthly_price:
                name += f" (${record.monthly_price}/month)"
            result.append((record.id, name))
        return result
