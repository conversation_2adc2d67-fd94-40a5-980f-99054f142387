<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">

        <!-- Default Server -->
        <record id="saas_server_default" model="saas.server">
            <field name="name">Default Server</field>
            <field name="code">default</field>
            <field name="host">localhost</field>
            <field name="username">odoo</field>
            <field name="server_type">instance</field>
            <field name="max_instances">50</field>
            <field name="cpu_cores">4</field>
            <field name="memory_gb">8</field>
            <field name="storage_gb">100</field>
            <field name="sequence">10</field>
        </record>

        <!-- Default SaaS Plans -->
        <record id="saas_plan_trial" model="saas.plan">
            <field name="name">Trial Plan</field>
            <field name="code">trial</field>
            <field name="plan_type">trial</field>
            <field name="monthly_price">0</field>
            <field name="yearly_price">0</field>
            <field name="max_users">3</field>
            <field name="max_storage_gb">1</field>
            <field name="cpu_limit">0.5</field>
            <field name="memory_limit_mb">512</field>
            <field name="trial_duration_days">15</field>
            <field name="backup_enabled">False</field>
            <field name="ssl_enabled">True</field>
            <field name="custom_domain_enabled">False</field>
            <field name="api_access_enabled">False</field>
            <field name="support_level">community</field>
            <field name="server_id" ref="saas_server_default"/>
            <field name="sequence">10</field>
        </record>

        <record id="saas_plan_basic" model="saas.plan">
            <field name="name">Basic Plan</field>
            <field name="code">basic</field>
            <field name="plan_type">basic</field>
            <field name="monthly_price">29</field>
            <field name="yearly_price">290</field>
            <field name="max_users">10</field>
            <field name="max_storage_gb">10</field>
            <field name="cpu_limit">1.0</field>
            <field name="memory_limit_mb">1024</field>
            <field name="backup_enabled">True</field>
            <field name="backup_retention_days">30</field>
            <field name="ssl_enabled">True</field>
            <field name="custom_domain_enabled">False</field>
            <field name="api_access_enabled">False</field>
            <field name="support_level">email</field>
            <field name="server_id" ref="saas_server_default"/>
            <field name="sequence">20</field>
        </record>

        <record id="saas_plan_standard" model="saas.plan">
            <field name="name">Standard Plan</field>
            <field name="code">standard</field>
            <field name="plan_type">standard</field>
            <field name="monthly_price">79</field>
            <field name="yearly_price">790</field>
            <field name="max_users">50</field>
            <field name="max_storage_gb">50</field>
            <field name="cpu_limit">2.0</field>
            <field name="memory_limit_mb">2048</field>
            <field name="backup_enabled">True</field>
            <field name="backup_retention_days">60</field>
            <field name="ssl_enabled">True</field>
            <field name="custom_domain_enabled">True</field>
            <field name="api_access_enabled">True</field>
            <field name="support_level">priority</field>
            <field name="server_id" ref="saas_server_default"/>
            <field name="sequence">30</field>
        </record>

        <record id="saas_plan_premium" model="saas.plan">
            <field name="name">Premium Plan</field>
            <field name="code">premium</field>
            <field name="plan_type">premium</field>
            <field name="monthly_price">199</field>
            <field name="yearly_price">1990</field>
            <field name="max_users">0</field>
            <field name="max_storage_gb">200</field>
            <field name="cpu_limit">4.0</field>
            <field name="memory_limit_mb">4096</field>
            <field name="backup_enabled">True</field>
            <field name="backup_retention_days">90</field>
            <field name="ssl_enabled">True</field>
            <field name="custom_domain_enabled">True</field>
            <field name="api_access_enabled">True</field>
            <field name="support_level">dedicated</field>
            <field name="server_id" ref="saas_server_default"/>
            <field name="sequence">40</field>
        </record>

        <!-- Default SaaS Apps -->
        <record id="saas_app_accounting" model="saas.app">
            <field name="name">Accounting</field>
            <field name="technical_name">account</field>
            <field name="category">accounting</field>
            <field name="description">Complete accounting solution</field>
            <field name="monthly_price">10</field>
            <field name="yearly_price">100</field>
            <field name="sequence">10</field>
        </record>

        <record id="saas_app_sales" model="saas.app">
            <field name="name">Sales</field>
            <field name="technical_name">sale</field>
            <field name="category">sales</field>
            <field name="description">Sales management and CRM</field>
            <field name="monthly_price">15</field>
            <field name="yearly_price">150</field>
            <field name="sequence">20</field>
        </record>

        <record id="saas_app_inventory" model="saas.app">
            <field name="name">Inventory</field>
            <field name="technical_name">stock</field>
            <field name="category">inventory</field>
            <field name="description">Inventory and warehouse management</field>
            <field name="monthly_price">12</field>
            <field name="yearly_price">120</field>
            <field name="sequence">30</field>
        </record>

        <record id="saas_app_website" model="saas.app">
            <field name="name">Website Builder</field>
            <field name="technical_name">website</field>
            <field name="category">website</field>
            <field name="description">Website builder and content management</field>
            <field name="monthly_price">8</field>
            <field name="yearly_price">80</field>
            <field name="sequence">40</field>
        </record>

        <record id="saas_app_ecommerce" model="saas.app">
            <field name="name">E-commerce</field>
            <field name="technical_name">website_sale</field>
            <field name="category">ecommerce</field>
            <field name="description">Online store and e-commerce</field>
            <field name="monthly_price">20</field>
            <field name="yearly_price">200</field>
            <field name="sequence">50</field>
        </record>

        <!-- Default Domain -->
        <record id="saas_domain_default" model="saas.domain">
            <field name="name">saas-demo.com</field>
            <field name="server_id" ref="saas_server_default"/>
            <field name="ssl_enabled">True</field>
            <field name="wildcard_ssl">True</field>
            <field name="dns_provider">manual</field>
            <field name="sequence">10</field>
        </record>

    </data>
</odoo>
