from odoo import models, fields, api
from datetime import datetime, timedelta

class SaasInstance(models.Model):
    _name = 'saas.instance'
    _description = 'SaaS Instance'
    _order = 'created_date desc'
    
    # Basic Information
    name = fields.Char(
        string='Instance Name', 
        required=True,
        help='Name of the SaaS instance'
    )
    
    subdomain = fields.Char(
        string='Subdomain',
        required=True,
        help='Subdomain for accessing the instance (e.g., client1.yoursaas.com)'
    )
    
    # Relationships
    client_id = fields.Many2one(
        'saas.client',
        string='Client',
        required=True,
        ondelete='cascade',
        help='Client who owns this instance'
    )
    
    plan_id = fields.Many2one(
        'saas.plan',
        string='Subscription Plan',
        required=True,
        help='Current subscription plan for this instance'
    )
    
    # Status and Dates
    status = fields.Selection([
        ('draft', 'Draft'),
        ('provisioning', 'Provisioning'),
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('expired', 'Expired'),
        ('terminated', 'Terminated'),
    ], string='Status', default='draft', required=True)
    
    created_date = fields.Datetime(
        string='Created Date',
        default=fields.Datetime.now,
        readonly=True
    )
    
    activated_date = fields.Datetime(
        string='Activated Date',
        readonly=True
    )
    
    expiry_date = fields.Datetime(
        string='Expiry Date',
        help='When the current subscription expires'
    )
    
    # Technical Information
    database_name = fields.Char(
        string='Database Name',
        help='Name of the database for this instance'
    )
    
    url = fields.Char(
        string='Instance URL',
        compute='_compute_url',
        store=True,
        help='Full URL to access the instance'
    )
    
    # Usage Statistics
    current_users = fields.Integer(
        string='Current Users',
        default=0,
        help='Number of active users in this instance'
    )
    
    storage_used_gb = fields.Float(
        string='Storage Used (GB)',
        default=0.0,
        help='Current storage usage in GB'
    )
    
    # Computed Fields
    days_until_expiry = fields.Integer(
        string='Days Until Expiry',
        compute='_compute_days_until_expiry',
        help='Number of days until subscription expires'
    )
    
    usage_percentage = fields.Float(
        string='Storage Usage %',
        compute='_compute_usage_percentage',
        help='Percentage of storage limit used'
    )
    
    is_over_limit = fields.Boolean(
        string='Over Limit',
        compute='_compute_over_limit',
        help='Whether instance is over user or storage limits'
    )
    
    @api.depends('subdomain')
    def _compute_url(self):
        for record in self:
            if record.subdomain:
                # You can customize the domain here
                record.url = f"https://{record.subdomain}.yoursaas.com"
            else:
                record.url = False
    
    @api.depends('expiry_date')
    def _compute_days_until_expiry(self):
        for record in self:
            if record.expiry_date:
                delta = record.expiry_date.date() - fields.Date.today()
                record.days_until_expiry = delta.days
            else:
                record.days_until_expiry = 0
    
    @api.depends('storage_used_gb', 'plan_id.max_storage_gb')
    def _compute_usage_percentage(self):
        for record in self:
            if record.plan_id.max_storage_gb > 0:
                record.usage_percentage = (record.storage_used_gb / record.plan_id.max_storage_gb) * 100
            else:
                record.usage_percentage = 0.0
    
    @api.depends('current_users', 'plan_id.max_users', 'storage_used_gb', 'plan_id.max_storage_gb')
    def _compute_over_limit(self):
        for record in self:
            user_over_limit = record.current_users > record.plan_id.max_users
            storage_over_limit = record.storage_used_gb > record.plan_id.max_storage_gb
            record.is_over_limit = user_over_limit or storage_over_limit
    
    # Action Methods
    def action_provision(self):
        """Start provisioning the instance"""
        self.write({
            'status': 'provisioning',
            'database_name': f"db_{self.subdomain}"
        })
        return True
    
    def action_activate(self):
        """Activate the instance"""
        self.write({
            'status': 'active',
            'activated_date': fields.Datetime.now()
        })
        return True
    
    def action_suspend(self):
        """Suspend the instance"""
        self.write({'status': 'suspended'})
        return True
    
    def action_terminate(self):
        """Terminate the instance"""
        self.write({'status': 'terminated'})
        return True
    
    # Constraints and Validations
    @api.constrains('subdomain')
    def _check_subdomain_unique(self):
        for record in self:
            if record.subdomain:
                existing = self.search([
                    ('subdomain', '=', record.subdomain),
                    ('id', '!=', record.id)
                ])
                if existing:
                    raise models.ValidationError(f"Subdomain '{record.subdomain}' is already in use.")
    
    # Display name
    def name_get(self):
        result = []
        for record in self:
            name = f"{record.name} ({record.subdomain})"
            result.append((record.id, name))
        return result
