# -*- coding: utf-8 -*-
{
    'name': 'SaaS Master - Quản Lý SaaS Toàn Diện',
    'version': '********.0',
    'category': 'Dịch Vụ/SaaS',
    'summary': '<PERSON><PERSON> thống quản lý SaaS toàn diện cho ứng dụng đa khách hàng',
    'description': """
SaaS Master - Hệ Thống Quản Lý SaaS Hoàn Chỉnh
===============================================

Hệ thống quản lý SaaS toàn diện cho Odoo 18 CE với hỗ trợ kiến trúc đa khách hàng.

Tính Năng Chính:
----------------
* **Quản Lý Khách Hàng**: Quản lý khách hàng và cơ sở dữ liệu hoàn chỉnh
* **<PERSON><PERSON><PERSON>**: <PERSON><PERSON><PERSON> hình gói đăng ký linh hoạt
* **Quản Lý Instance**: <PERSON>i<PERSON><PERSON> khiển instance cơ sở dữ liệu đa khách hàng
* **Quản Lý Server**: Quản trị server hosting
* **Thanh Toán & Hóa Đơn**: Quản lý thanh toán và đăng ký tích hợp
* **Dashboard Quản Trị**: Giao diện quản lý toàn diện
* **Portal Khách Hàng**: Giao diện tự phục vụ cho khách hàng

Tính Năng Kỹ Thuật:
-------------------
* **Tương Thích Odoo 18**: Được xây dựng cho Odoo 18 CE không phụ thuộc subscription module
* **Bảo Mật**: Kiểm soát truy cập dựa trên vai trò và cô lập dữ liệu
* **Kiến Trúc Mở Rộng**: Thiết kế cho triển khai khối lượng lớn
* **Phát Triển Tăng Dần**: Thiết kế modular cho triển khai từng bước
    """,
    'author': 'SaaS Master Team',
    'website': 'https://www.saas-master.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'mail',
        'portal',
        'website',
        'account',
        'payment',
        'contacts',
    ],
    'data': [
        # Views will be added incrementally
        'views/saas_menu.xml',
        'views/saas_client_views.xml',
        'views/saas_plan_views.xml',
        'views/saas_instance_views.xml',
        'views/saas_server_views.xml',
        'views/saas_dashboard_views.xml',
        # Security files (temporarily disabled for testing)
        # 'security/security.xml',
        # 'security/ir.model.access.csv',
    ],
    'demo': [],
    'assets': {},
    'images': [],
    'installable': True,
    'auto_install': False,
    'application': True,
}
