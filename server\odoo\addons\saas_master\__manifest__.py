# -*- coding: utf-8 -*-
{
    'name': 'SaaS Master',
    'version': '********.0',
    'category': 'Tools',
    'summary': 'Complete SaaS Management System',
    'description': """
SaaS Master - Complete SaaS Management System
=============================================

A comprehensive SaaS management system for Odoo 18 CE with multi-tenant architecture support.

Key Features:
------------
* **Tenant Management**: Complete client and database management
* **Service Plans**: Flexible subscription plan configuration  
* **Instance Management**: Multi-tenant database instance control
* **Server Management**: Hosting server administration
* **Billing & Invoicing**: Integrated payment and subscription management
* **Admin Dashboard**: Comprehensive management interface
* **Customer Portal**: Self-service customer interface

Technical Features:
-------------------
* **Odoo 18 Compatible**: Built for Odoo 18 CE without subscription module dependency
* **Security**: Role-based access control and data isolation
* **Scalable Architecture**: Designed for high-volume deployments
* **Incremental Development**: Modular design for step-by-step implementation
    """,
    'author': 'SaaS Master Team',
    'website': 'https://www.saas-master.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'mail',
        'portal',
        'website',
        'account',
        'payment',
        'contacts',
    ],
    'data': [
        # Views will be added incrementally
        'views/saas_menu.xml',
        'views/saas_client_views.xml',
        'views/saas_plan_views.xml',
        'views/saas_instance_views.xml',
        'views/saas_server_views.xml',
        'views/saas_dashboard_views.xml',
        # Security files (temporarily disabled for testing)
        # 'security/security.xml',
        # 'security/ir.model.access.csv',
    ],
    'demo': [],
    'assets': {},
    'images': [],
    'installable': True,
    'auto_install': False,
    'application': True,
}
