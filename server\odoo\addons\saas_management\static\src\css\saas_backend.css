/* SaaS Management Backend Styles */

/* ===== DASHBOARD STYLES ===== */
.saas-dashboard-container {
    padding: 20px;
    background: #f8f9fa;
    min-height: 100vh;
}

.saas-dashboard-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 12px;
    margin-bottom: 30px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.saas-dashboard-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.saas-dashboard-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.saas-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.saas-stat-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
    transition: transform 0.3s ease;
}

.saas-stat-card:hover {
    transform: translateY(-5px);
}

.saas-stat-card.revenue {
    border-left-color: #28a745;
}

.saas-stat-card.instances {
    border-left-color: #17a2b8;
}

.saas-stat-card.clients {
    border-left-color: #ffc107;
}

.saas-stat-card.servers {
    border-left-color: #dc3545;
}

.saas-stat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
}

.saas-stat-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.saas-stat-icon.revenue {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.saas-stat-icon.instances {
    background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
}

.saas-stat-icon.clients {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.saas-stat-icon.servers {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
}

.saas-stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 5px;
}

.saas-stat-label {
    color: #666;
    font-size: 1rem;
    margin-bottom: 10px;
}

.saas-stat-change {
    font-size: 0.9rem;
    font-weight: 600;
}

.saas-stat-change.positive {
    color: #28a745;
}

.saas-stat-change.negative {
    color: #dc3545;
}

/* ===== CHARTS SECTION ===== */
.saas-charts-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.saas-chart-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.saas-chart-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

.saas-chart-container {
    height: 300px;
    position: relative;
}

/* ===== RECENT ACTIVITY ===== */
.saas-activity-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.saas-activity-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

.saas-activity-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.saas-activity-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.saas-activity-item:last-child {
    border-bottom: none;
}

.saas-activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.1rem;
    color: white;
}

.saas-activity-icon.instance {
    background: #17a2b8;
}

.saas-activity-icon.client {
    background: #ffc107;
}

.saas-activity-icon.payment {
    background: #28a745;
}

.saas-activity-icon.backup {
    background: #6f42c1;
}

.saas-activity-content {
    flex: 1;
}

.saas-activity-text {
    color: #333;
    margin-bottom: 5px;
}

.saas-activity-time {
    color: #666;
    font-size: 0.9rem;
}

/* ===== FORM STYLES ===== */
.saas-form-container {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.saas-form-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #eee;
}

.saas-form-group {
    margin-bottom: 20px;
}

.saas-form-label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.saas-form-input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.saas-form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.saas-form-select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
    transition: border-color 0.3s ease;
}

.saas-form-textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    min-height: 100px;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.saas-form-help {
    font-size: 0.9rem;
    color: #666;
    margin-top: 5px;
}

/* ===== BUTTONS ===== */
.saas-btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.saas-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.saas-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.saas-btn-secondary {
    background: #6c757d;
    color: white;
}

.saas-btn-success {
    background: #28a745;
    color: white;
}

.saas-btn-danger {
    background: #dc3545;
    color: white;
}

.saas-btn-warning {
    background: #ffc107;
    color: #212529;
}

.saas-btn-info {
    background: #17a2b8;
    color: white;
}

.saas-btn-small {
    padding: 8px 16px;
    font-size: 0.9rem;
}

.saas-btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
}

/* ===== TABLES ===== */
.saas-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.saas-table th {
    background: #f8f9fa;
    padding: 15px;
    text-align: left;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #dee2e6;
}

.saas-table td {
    padding: 15px;
    border-bottom: 1px solid #dee2e6;
}

.saas-table tr:hover {
    background: #f8f9fa;
}

/* ===== STATUS BADGES ===== */
.saas-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.saas-status.active {
    background: #d4edda;
    color: #155724;
}

.saas-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.saas-status.trial {
    background: #fff3cd;
    color: #856404;
}

.saas-status.suspended {
    background: #f1c0c7;
    color: #721c24;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .saas-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .saas-charts-section {
        grid-template-columns: 1fr;
    }
    
    .saas-dashboard-container {
        padding: 10px;
    }
    
    .saas-dashboard-header {
        padding: 20px;
    }
    
    .saas-dashboard-title {
        font-size: 1.8rem;
    }
}

/* ===== LOADING STATES ===== */
.saas-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.saas-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}
