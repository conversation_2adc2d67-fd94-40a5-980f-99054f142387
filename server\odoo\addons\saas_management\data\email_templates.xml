<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Client Welcome Email Template -->
        <record id="email_template_client_welcome" model="mail.template">
            <field name="name">SaaS Client Welcome</field>
            <field name="model_id" ref="model_saas_client"/>
            <field name="subject">Welcome to {{ object.company_id.name or 'Our SaaS Platform' }}!</field>
            <field name="email_from">{{ (object.company_id.email or user.email) }}</field>
            <field name="email_to">{{ object.email }}</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <div style="margin: 0px; padding: 0px; background-color: #f8f9fa;">
        <div style="margin: 0px auto; max-width: 600px; padding: 20px;">
            <div style="background-color: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h2 style="color: #2c3e50; margin-bottom: 20px;">Welcome to Our SaaS Platform!</h2>
                
                <p>Dear {{ object.name }},</p>
                
                <p>Thank you for joining our SaaS platform! Your account has been successfully created.</p>
                
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4 style="margin-top: 0;">Account Details:</h4>
                    <ul>
                        <li><strong>Client Name:</strong> {{ object.name }}</li>
                        <li><strong>Email:</strong> {{ object.email }}</li>
                        <li><strong>Registration Date:</strong> {{ object.registration_date.strftime('%B %d, %Y') if object.registration_date else 'N/A' }}</li>
                    </ul>
                </div>
                
                <p>You can access your portal using the link below:</p>
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{{ object.get_portal_url() }}" 
                       style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                        Access Your Portal
                    </a>
                </div>
                
                <p>If you have any questions, please don't hesitate to contact our support team.</p>
                
                <p>Best regards,<br/>
                The SaaS Team</p>
            </div>
        </div>
    </div>
</div>
            </field>
        </record>

        <!-- Instance Ready Email Template -->
        <record id="email_template_instance_ready" model="mail.template">
            <field name="name">SaaS Instance Ready</field>
            <field name="model_id" ref="model_saas_instance"/>
            <field name="subject">Your {{ object.plan_id.name }} instance is ready!</field>
            <field name="email_from">{{ (object.company_id.email or user.email) }}</field>
            <field name="email_to">{{ object.client_id.email }}</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <div style="margin: 0px; padding: 0px; background-color: #f8f9fa;">
        <div style="margin: 0px auto; max-width: 600px; padding: 20px;">
            <div style="background-color: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h2 style="color: #28a745; margin-bottom: 20px;">🎉 Your Instance is Ready!</h2>
                
                <p>Dear {{ object.client_id.name }},</p>
                
                <p>Great news! Your {{ object.plan_id.name }} instance has been successfully created and is now ready to use.</p>
                
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4 style="margin-top: 0;">Instance Details:</h4>
                    <ul>
                        <li><strong>Instance Name:</strong> {{ object.name }}</li>
                        <li><strong>Plan:</strong> {{ object.plan_id.name }}</li>
                        <li><strong>URL:</strong> <a href="{{ object.instance_url }}">{{ object.instance_url }}</a></li>
                        <li><strong>Admin Password:</strong> {{ object.admin_password }}</li>
                    </ul>
                </div>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{{ object.instance_url }}" 
                       style="background-color: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                        Access Your Instance
                    </a>
                </div>
                
                <p><strong>Important:</strong> Please save your admin password in a secure location. You can change it after your first login.</p>
                
                <p>If you need any assistance, please contact our support team.</p>
                
                <p>Best regards,<br/>
                The SaaS Team</p>
            </div>
        </div>
    </div>
</div>
            </field>
        </record>

        <!-- Trial Expiring Email Template -->
        <record id="email_template_trial_expiring" model="mail.template">
            <field name="name">SaaS Trial Expiring</field>
            <field name="model_id" ref="model_saas_instance"/>
            <field name="subject">Your trial expires in {{ object.days_until_trial_end }} days</field>
            <field name="email_from">{{ (object.company_id.email or user.email) }}</field>
            <field name="email_to">{{ object.client_id.email }}</field>
            <field name="body_html" type="html">
<div style="margin: 0px; padding: 0px;">
    <div style="margin: 0px; padding: 0px; background-color: #f8f9fa;">
        <div style="margin: 0px auto; max-width: 600px; padding: 20px;">
            <div style="background-color: white; border-radius: 8px; padding: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h2 style="color: #ffc107; margin-bottom: 20px;">⏰ Your Trial is Expiring Soon</h2>
                
                <p>Dear {{ object.client_id.name }},</p>
                
                <p>This is a friendly reminder that your trial instance will expire in <strong>{{ object.days_until_trial_end }} days</strong>.</p>
                
                <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
                    <h4 style="margin-top: 0;">Trial Details:</h4>
                    <ul>
                        <li><strong>Instance:</strong> {{ object.name }}</li>
                        <li><strong>Expiry Date:</strong> {{ object.trial_end_date.strftime('%B %d, %Y at %I:%M %p') if object.trial_end_date else 'N/A' }}</li>
                        <li><strong>Days Remaining:</strong> {{ object.days_until_trial_end }}</li>
                    </ul>
                </div>
                
                <p>To continue using your instance, please upgrade to a paid plan before the trial expires.</p>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{{ object.client_id.get_portal_url() }}" 
                       style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                        Upgrade Now
                    </a>
                </div>
                
                <p>If you have any questions about our plans or need assistance, please contact our support team.</p>
                
                <p>Best regards,<br/>
                The SaaS Team</p>
            </div>
        </div>
    </div>
</div>
            </field>
        </record>

    </data>
</odoo>
