# -*- coding: utf-8 -*-

from odoo import models, fields, api
import uuid


class SaasInstance(models.Model):
    _name = 'saas.instance'
    _description = 'SaaS Database Instance'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(
        string='Instance Name',
        required=True,
        tracking=True,
        help='Name of the database instance'
    )
    
    subdomain = fields.Char(
        string='Subdomain',
        required=True,
        tracking=True,
        help='Subdomain for accessing the instance'
    )
    
    database_name = fields.Char(
        string='Database Name',
        required=True,
        tracking=True,
        help='Name of the database'
    )
    
    state = fields.Selection([
        ('draft', 'Draft'),
        ('creating', 'Creating'),
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('maintenance', 'Maintenance'),
        ('terminated', 'Terminated'),
    ], string='Status', default='draft', required=True, tracking=True)
    
    # Relationships
    client_id = fields.Many2one(
        'saas.client',
        string='Client',
        required=True,
        tracking=True,
        help='Client who owns this instance'
    )
    
    plan_id = fields.Many2one(
        'saas.plan',
        string='Service Plan',
        required=True,
        tracking=True,
        help='Service plan for this instance'
    )
    
    server_id = fields.Many2one(
        'saas.server',
        string='Server',
        required=True,
        tracking=True,
        help='Server hosting this instance'
    )
    
    # Instance Details
    url = fields.Char(
        string='Instance URL',
        compute='_compute_url',
        store=True,
        help='Full URL to access the instance'
    )
    
    admin_login = fields.Char(
        string='Admin Login',
        default='admin',
        help='Administrator login for the instance'
    )
    
    admin_password = fields.Char(
        string='Admin Password',
        help='Administrator password for the instance'
    )
    
    # Technical Information
    odoo_version = fields.Char(
        string='Odoo Version',
        default='18.0',
        help='Odoo version for this instance'
    )
    
    modules = fields.Text(
        string='Installed Modules',
        help='List of installed modules'
    )
    
    # Resource Usage
    storage_used_gb = fields.Float(
        string='Storage Used (GB)',
        default=0.0,
        help='Storage currently used in GB'
    )
    
    users_count = fields.Integer(
        string='Users Count',
        default=1,
        help='Number of users in the instance'
    )
    
    # Dates
    created_date = fields.Datetime(
        string='Created Date',
        default=fields.Datetime.now,
        tracking=True,
        help='Date when instance was created'
    )
    
    last_backup_date = fields.Datetime(
        string='Last Backup',
        help='Date of last backup'
    )
    
    expiry_date = fields.Date(
        string='Expiry Date',
        tracking=True,
        help='Instance expiry date'
    )
    
    # Configuration
    custom_domain = fields.Char(
        string='Custom Domain',
        help='Custom domain for the instance'
    )
    
    ssl_enabled = fields.Boolean(
        string='SSL Enabled',
        default=True,
        help='SSL certificate enabled'
    )
    
    backup_enabled = fields.Boolean(
        string='Backup Enabled',
        default=True,
        help='Automatic backup enabled'
    )
    
    active = fields.Boolean(
        string='Active',
        default=True,
        help='Uncheck to archive the instance'
    )
    
    # Computed fields
    days_until_expiry = fields.Integer(
        string='Days Until Expiry',
        compute='_compute_days_until_expiry',
        help='Number of days until expiry'
    )
    
    storage_usage_percent = fields.Float(
        string='Storage Usage %',
        compute='_compute_storage_usage_percent',
        help='Storage usage percentage'
    )
    
    @api.depends('subdomain', 'server_id.domain', 'custom_domain')
    def _compute_url(self):
        for instance in self:
            if instance.custom_domain:
                protocol = 'https' if instance.ssl_enabled else 'http'
                instance.url = f"{protocol}://{instance.custom_domain}"
            elif instance.subdomain and instance.server_id.domain:
                protocol = 'https' if instance.ssl_enabled else 'http'
                instance.url = f"{protocol}://{instance.subdomain}.{instance.server_id.domain}"
            else:
                instance.url = False
    
    @api.depends('expiry_date')
    def _compute_days_until_expiry(self):
        today = fields.Date.today()
        for instance in self:
            if instance.expiry_date:
                delta = instance.expiry_date - today
                instance.days_until_expiry = delta.days
            else:
                instance.days_until_expiry = 0
    
    @api.depends('storage_used_gb', 'plan_id.max_storage_gb')
    def _compute_storage_usage_percent(self):
        for instance in self:
            if instance.plan_id.max_storage_gb > 0:
                instance.storage_usage_percent = (instance.storage_used_gb / instance.plan_id.max_storage_gb) * 100
            else:
                instance.storage_usage_percent = 0
    
    @api.model
    def create(self, vals):
        """Generate unique subdomain and database name if not provided"""
        if not vals.get('subdomain'):
            vals['subdomain'] = self._generate_unique_subdomain()
        if not vals.get('database_name'):
            vals['database_name'] = f"saas_{vals['subdomain']}"
        if not vals.get('admin_password'):
            vals['admin_password'] = str(uuid.uuid4())[:8]
        return super().create(vals)
    
    def _generate_unique_subdomain(self):
        """Generate a unique subdomain"""
        base = str(uuid.uuid4())[:8]
        while self.search([('subdomain', '=', base)]):
            base = str(uuid.uuid4())[:8]
        return base
    
    def action_create_instance(self):
        """Create the database instance"""
        self.write({'state': 'creating'})
        # Here you would implement the actual database creation logic
        # For now, we'll just simulate it
        self.write({'state': 'active'})
    
    def action_suspend(self):
        """Suspend the instance"""
        self.write({'state': 'suspended'})
    
    def action_activate(self):
        """Activate the instance"""
        self.write({'state': 'active'})
    
    def action_maintenance(self):
        """Put instance in maintenance mode"""
        self.write({'state': 'maintenance'})
    
    def action_terminate(self):
        """Terminate the instance"""
        self.write({'state': 'terminated', 'active': False})
    
    def action_backup(self):
        """Create backup of the instance"""
        self.write({'last_backup_date': fields.Datetime.now()})
        # Here you would implement the actual backup logic
    
    @api.constrains('subdomain')
    def _check_subdomain_unique(self):
        for instance in self:
            if instance.subdomain:
                existing = self.search([
                    ('subdomain', '=', instance.subdomain),
                    ('id', '!=', instance.id)
                ])
                if existing:
                    raise ValueError(f"Subdomain '{instance.subdomain}' already exists")
    
    @api.constrains('users_count', 'storage_used_gb')
    def _check_plan_limits(self):
        for instance in self:
            if instance.plan_id:
                if instance.users_count > instance.plan_id.max_users:
                    raise ValueError(f"Users count exceeds plan limit ({instance.plan_id.max_users})")
                if instance.storage_used_gb > instance.plan_id.max_storage_gb:
                    raise ValueError(f"Storage usage exceeds plan limit ({instance.plan_id.max_storage_gb} GB)")
