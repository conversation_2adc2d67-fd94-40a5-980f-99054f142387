# -*- coding: utf-8 -*-

from odoo import models, fields, api
import uuid


class SaasInstance(models.Model):
    _name = 'saas.instance'
    _description = 'Instance Database SaaS'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(
        string='Tên Instance',
        required=True,
        tracking=True,
        help='Tên của instance database'
    )

    subdomain = fields.Char(
        string='Tên Miền <PERSON>',
        required=True,
        tracking=True,
        help='Tên miền phụ để truy cập instance'
    )

    database_name = fields.Char(
        string='Tên Database',
        required=True,
        tracking=True,
        help='Tên của database'
    )

    state = fields.Selection([
        ('draft', 'Nháp'),
        ('creating', '<PERSON>ang <PERSON>'),
        ('active', 'Hoạt Động'),
        ('suspended', 'Tạm Dừng'),
        ('maintenance', 'Bảo Trì'),
        ('terminated', '<PERSON><PERSON>'),
    ], string='Trạng Thá<PERSON>', default='draft', required=True, tracking=True)
    
    # Relationships
    client_id = fields.Many2one(
        'saas.client',
        string='Khách Hàng',
        required=True,
        tracking=True,
        help='Khách hàng sở hữu instance này'
    )

    plan_id = fields.Many2one(
        'saas.plan',
        string='Gói Dịch Vụ',
        required=True,
        tracking=True,
        help='Gói dịch vụ cho instance này'
    )

    server_id = fields.Many2one(
        'saas.server',
        string='Server',
        required=True,
        tracking=True,
        help='Server lưu trữ instance này'
    )

    # Instance Details
    url = fields.Char(
        string='URL Instance',
        compute='_compute_url',
        store=True,
        help='URL đầy đủ để truy cập instance'
    )

    admin_login = fields.Char(
        string='Tài Khoản Admin',
        default='admin',
        help='Tài khoản quản trị viên cho instance'
    )

    admin_password = fields.Char(
        string='Mật Khẩu Admin',
        help='Mật khẩu quản trị viên cho instance'
    )
    
    # Technical Information
    odoo_version = fields.Char(
        string='Phiên Bản Odoo',
        default='18.0',
        help='Phiên bản Odoo cho instance này'
    )

    modules = fields.Text(
        string='Modules Đã Cài',
        help='Danh sách các modules đã cài đặt'
    )

    # Resource Usage
    storage_used_gb = fields.Float(
        string='Dung Lượng Đã Dùng (GB)',
        default=0.0,
        help='Dung lượng hiện tại đã sử dụng tính bằng GB'
    )

    users_count = fields.Integer(
        string='Số Lượng Người Dùng',
        default=1,
        help='Số lượng người dùng trong instance'
    )

    # Dates
    created_date = fields.Datetime(
        string='Ngày Tạo',
        default=fields.Datetime.now,
        tracking=True,
        help='Ngày tạo instance'
    )

    last_backup_date = fields.Datetime(
        string='Sao Lưu Cuối',
        help='Ngày sao lưu cuối cùng'
    )

    expiry_date = fields.Date(
        string='Ngày Hết Hạn',
        tracking=True,
        help='Ngày hết hạn của instance'
    )
    
    # Configuration
    custom_domain = fields.Char(
        string='Tên Miền Tùy Chỉnh',
        help='Tên miền tùy chỉnh cho instance'
    )

    ssl_enabled = fields.Boolean(
        string='SSL Được Bật',
        default=True,
        help='Chứng chỉ SSL được kích hoạt'
    )

    backup_enabled = fields.Boolean(
        string='Sao Lưu Được Bật',
        default=True,
        help='Sao lưu tự động được kích hoạt'
    )

    active = fields.Boolean(
        string='Hoạt Động',
        default=True,
        help='Bỏ chọn để lưu trữ instance'
    )

    # Computed fields
    days_until_expiry = fields.Integer(
        string='Số Ngày Đến Hết Hạn',
        compute='_compute_days_until_expiry',
        help='Số ngày còn lại đến khi hết hạn'
    )

    storage_usage_percent = fields.Float(
        string='% Sử Dụng Dung Lượng',
        compute='_compute_storage_usage_percent',
        help='Phần trăm sử dụng dung lượng'
    )
    
    @api.depends('subdomain', 'server_id.domain', 'custom_domain')
    def _compute_url(self):
        for instance in self:
            if instance.custom_domain:
                protocol = 'https' if instance.ssl_enabled else 'http'
                instance.url = f"{protocol}://{instance.custom_domain}"
            elif instance.subdomain and instance.server_id.domain:
                protocol = 'https' if instance.ssl_enabled else 'http'
                instance.url = f"{protocol}://{instance.subdomain}.{instance.server_id.domain}"
            else:
                instance.url = False
    
    @api.depends('expiry_date')
    def _compute_days_until_expiry(self):
        today = fields.Date.today()
        for instance in self:
            if instance.expiry_date:
                delta = instance.expiry_date - today
                instance.days_until_expiry = delta.days
            else:
                instance.days_until_expiry = 0
    
    @api.depends('storage_used_gb', 'plan_id.max_storage_gb')
    def _compute_storage_usage_percent(self):
        for instance in self:
            if instance.plan_id.max_storage_gb > 0:
                instance.storage_usage_percent = (instance.storage_used_gb / instance.plan_id.max_storage_gb) * 100
            else:
                instance.storage_usage_percent = 0
    
    @api.model
    def create(self, vals):
        """Tạo tên miền phụ và tên database duy nhất nếu chưa được cung cấp"""
        if not vals.get('subdomain'):
            vals['subdomain'] = self._generate_unique_subdomain()
        if not vals.get('database_name'):
            vals['database_name'] = f"saas_{vals['subdomain']}"
        if not vals.get('admin_password'):
            vals['admin_password'] = str(uuid.uuid4())[:8]
        return super().create(vals)

    def _generate_unique_subdomain(self):
        """Tạo tên miền phụ duy nhất"""
        base = str(uuid.uuid4())[:8]
        while self.search([('subdomain', '=', base)]):
            base = str(uuid.uuid4())[:8]
        return base

    def action_create_instance(self):
        """Tạo instance database"""
        self.write({'state': 'creating'})
        # Ở đây bạn sẽ triển khai logic tạo database thực tế
        # Hiện tại, chúng ta chỉ mô phỏng nó
        self.write({'state': 'active'})

    def action_suspend(self):
        """Tạm dừng instance"""
        self.write({'state': 'suspended'})

    def action_activate(self):
        """Kích hoạt instance"""
        self.write({'state': 'active'})
    
    def action_maintenance(self):
        """Đưa instance vào chế độ bảo trì"""
        self.write({'state': 'maintenance'})

    def action_terminate(self):
        """Kết thúc instance"""
        self.write({'state': 'terminated', 'active': False})

    def action_backup(self):
        """Tạo bản sao lưu của instance"""
        self.write({'last_backup_date': fields.Datetime.now()})
        # Ở đây bạn sẽ triển khai logic sao lưu thực tế

    @api.constrains('subdomain')
    def _check_subdomain_unique(self):
        for instance in self:
            if instance.subdomain:
                existing = self.search([
                    ('subdomain', '=', instance.subdomain),
                    ('id', '!=', instance.id)
                ])
                if existing:
                    raise ValueError(f"Tên miền phụ '{instance.subdomain}' đã tồn tại")

    @api.constrains('users_count', 'storage_used_gb')
    def _check_plan_limits(self):
        for instance in self:
            if instance.plan_id:
                if instance.users_count > instance.plan_id.max_users:
                    raise ValueError(f"Số lượng người dùng vượt quá giới hạn gói ({instance.plan_id.max_users})")
                if instance.storage_used_gb > instance.plan_id.max_storage_gb:
                    raise ValueError(f"Dung lượng sử dụng vượt quá giới hạn gói ({instance.plan_id.max_storage_gb} GB)")
