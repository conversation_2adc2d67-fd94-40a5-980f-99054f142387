<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- SaaS Client List View -->
    <record id="view_saas_client_list" model="ir.ui.view">
        <field name="name">saas.client.list</field>
        <field name="model">saas.client</field>
        <field name="arch" type="xml">
            <list string="SaaS Clients">
                <field name="name"/>
                <field name="email"/>
                <field name="company_name"/>
                <field name="plan_id"/>
                <field name="state" widget="badge"
                       decoration-success="state == 'active'"
                       decoration-warning="state == 'suspended'"
                       decoration-danger="state == 'cancelled'"
                       decoration-info="state == 'draft'"/>
                <field name="partner_id"/>
            </list>
        </field>
    </record>

    <!-- SaaS Client Form View -->
    <record id="view_saas_client_form" model="ir.ui.view">
        <field name="name">saas.client.form</field>
        <field name="model">saas.client</field>
        <field name="arch" type="xml">
            <form string="SaaS Client">
                <header>
                    <button name="action_activate" string="Activate" type="object" 
                            class="btn-primary" invisible="state == 'active'"/>
                    <button name="action_suspend" string="Suspend" type="object" 
                            class="btn-warning" invisible="state != 'active'"/>
                    <button name="action_cancel" string="Cancel" type="object" 
                            class="btn-danger" invisible="state == 'cancelled'"/>
                    <button name="action_reset_to_draft" string="Reset to Draft" type="object" 
                            class="btn-secondary" invisible="state == 'draft'"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,active,suspended,cancelled"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_instances" type="object" class="oe_stat_button" icon="fa-database">
                            <field name="instance_count" widget="statinfo" string="Instances"/>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="name" placeholder="Client Name"/>
                            <field name="email" widget="email"/>
                            <field name="phone" widget="phone"/>
                        </group>
                        <group>
                            <field name="company_name"/>
                            <field name="plan_id"/>
                            <field name="partner_id" readonly="1"/>
                            <field name="active"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Notes">
                            <field name="notes" placeholder="Additional notes about the client..."/>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- SaaS Client Search View -->
    <record id="view_saas_client_search" model="ir.ui.view">
        <field name="name">saas.client.search</field>
        <field name="model">saas.client</field>
        <field name="arch" type="xml">
            <search string="Search SaaS Clients">
                <field name="name" string="Client Name"/>
                <field name="email"/>
                <field name="company_name"/>
                <field name="partner_id"/>
                <separator/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('active', '=', False)]"/>
                <separator/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Active Clients" name="active_clients" domain="[('state', '=', 'active')]"/>
                <filter string="Suspended" name="suspended" domain="[('state', '=', 'suspended')]"/>
                <filter string="Cancelled" name="cancelled" domain="[('state', '=', 'cancelled')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Company" name="group_company" context="{'group_by': 'company_name'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- SaaS Client Action -->
    <record id="action_saas_client" model="ir.actions.act_window">
        <field name="name">SaaS Clients</field>
        <field name="res_model">saas.client</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_saas_client_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first SaaS client!
            </p>
            <p>
                Manage your SaaS clients and their subscriptions from here.
                You can track client information, manage their status, and handle their service plans.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_saas_client"
              name="Clients"
              parent="menu_saas_master_root"
              action="action_saas_client"
              sequence="10"/>
</odoo>
