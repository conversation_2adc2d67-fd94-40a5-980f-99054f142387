/** @odoo-module **/

import { Component, useState, onMounted } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";

/**
 * SaaS Portal Component
 * Handles customer portal functionality for SaaS instances
 */
class SaasPortalComponent extends Component {
    setup() {
        this.rpc = useService("rpc");
        this.notification = useService("notification");
        this.dialog = useService("dialog");
        
        this.state = useState({
            instances: [],
            subscriptions: [],
            loading: true,
            selectedInstance: null,
            stats: {
                total_instances: 0,
                active_instances: 0,
                trial_instances: 0,
                total_users: 0
            }
        });

        onMounted(async () => {
            await this.loadPortalData();
        });
    }

    /**
     * Load portal data from backend
     */
    async loadPortalData() {
        try {
            this.state.loading = true;
            
            // Load instances and subscriptions
            const [instances, subscriptions, stats] = await Promise.all([
                this.loadInstances(),
                this.loadSubscriptions(),
                this.loadStats()
            ]);
            
            this.state.instances = instances;
            this.state.subscriptions = subscriptions;
            this.state.stats = stats;
            
        } catch (error) {
            console.error("Error loading portal data:", error);
            this.loadDemoData();
        } finally {
            this.state.loading = false;
            this.updatePortalDisplay();
        }
    }

    /**
     * Load instances from backend
     */
    async loadInstances() {
        try {
            return await this.rpc("/saas/api/portal/instances", {});
        } catch (error) {
            console.error("Error loading instances:", error);
            return this.getDemoInstances();
        }
    }

    /**
     * Load subscriptions from backend
     */
    async loadSubscriptions() {
        try {
            return await this.rpc("/saas/api/portal/subscriptions", {});
        } catch (error) {
            console.error("Error loading subscriptions:", error);
            return this.getDemoSubscriptions();
        }
    }

    /**
     * Load statistics from backend
     */
    async loadStats() {
        try {
            return await this.rpc("/saas/api/portal/stats", {});
        } catch (error) {
            console.error("Error loading stats:", error);
            return this.getDemoStats();
        }
    }

    /**
     * Load demo data when backend is not available
     */
    loadDemoData() {
        this.state.instances = this.getDemoInstances();
        this.state.subscriptions = this.getDemoSubscriptions();
        this.state.stats = this.getDemoStats();
    }

    /**
     * Get demo instances
     */
    getDemoInstances() {
        return [
            {
                id: 1,
                name: "My Production Store",
                domain: "mystore.saas-demo.com",
                status: "active",
                plan: "Professional",
                created_date: "2024-01-15",
                users_count: 12,
                storage_used: "2.3 GB",
                last_backup: "2024-01-20 10:30:00"
            },
            {
                id: 2,
                name: "Development Environment",
                domain: "dev-mystore.saas-demo.com",
                status: "trial",
                plan: "Starter",
                created_date: "2024-01-18",
                users_count: 3,
                storage_used: "0.8 GB",
                last_backup: "2024-01-20 08:15:00"
            }
        ];
    }

    /**
     * Get demo subscriptions
     */
    getDemoSubscriptions() {
        return [
            {
                id: 1,
                plan_name: "Professional",
                status: "active",
                next_billing: "2024-02-15",
                amount: 79,
                billing_cycle: "monthly"
            }
        ];
    }

    /**
     * Get demo statistics
     */
    getDemoStats() {
        return {
            total_instances: 2,
            active_instances: 1,
            trial_instances: 1,
            total_users: 15
        };
    }

    /**
     * Update portal display
     */
    updatePortalDisplay() {
        this.updateStatsCards();
        this.updateInstancesList();
        this.updateSubscriptionsList();
    }

    /**
     * Update statistics cards
     */
    updateStatsCards() {
        const statsElements = {
            'total-instances': this.state.stats.total_instances,
            'active-instances': this.state.stats.active_instances,
            'trial-instances': this.state.stats.trial_instances,
            'total-users': this.state.stats.total_users
        };

        Object.entries(statsElements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    /**
     * Update instances list
     */
    updateInstancesList() {
        const container = document.querySelector('.saas-instances-list');
        if (!container) return;

        if (this.state.instances.length === 0) {
            container.innerHTML = `
                <div class="saas-empty-state">
                    <p>No instances found. <a href="/saas/pricing" class="saas-btn saas-btn-primary">Create your first instance</a></p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.state.instances.map(instance => `
            <div class="saas-instance-item" data-instance-id="${instance.id}">
                <div class="saas-instance-info">
                    <h4>${instance.name}</h4>
                    <p><strong>Domain:</strong> <a href="https://${instance.domain}" target="_blank">${instance.domain}</a></p>
                    <p><strong>Plan:</strong> ${instance.plan} | <strong>Users:</strong> ${instance.users_count} | <strong>Storage:</strong> ${instance.storage_used}</p>
                    <p><strong>Last Backup:</strong> ${this.formatDate(instance.last_backup)}</p>
                </div>
                <div class="saas-instance-actions">
                    <span class="saas-instance-status ${instance.status}">${this.formatStatus(instance.status)}</span>
                    <div class="saas-instance-buttons">
                        <button class="saas-btn saas-btn-primary saas-btn-small" onclick="saasPortal.accessInstance(${instance.id})">
                            Access
                        </button>
                        <button class="saas-btn saas-btn-secondary saas-btn-small" onclick="saasPortal.manageInstance(${instance.id})">
                            Manage
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Update subscriptions list
     */
    updateSubscriptionsList() {
        const container = document.querySelector('.saas-subscriptions-list');
        if (!container) return;

        if (this.state.subscriptions.length === 0) {
            container.innerHTML = `
                <div class="saas-empty-state">
                    <p>No active subscriptions.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.state.subscriptions.map(subscription => `
            <div class="saas-subscription-item">
                <div class="saas-subscription-info">
                    <h4>${subscription.plan_name} Plan</h4>
                    <p><strong>Status:</strong> <span class="saas-status ${subscription.status}">${this.formatStatus(subscription.status)}</span></p>
                    <p><strong>Next Billing:</strong> ${this.formatDate(subscription.next_billing)}</p>
                    <p><strong>Amount:</strong> $${subscription.amount}/${subscription.billing_cycle}</p>
                </div>
                <div class="saas-subscription-actions">
                    <button class="saas-btn saas-btn-secondary saas-btn-small" onclick="saasPortal.manageSubscription(${subscription.id})">
                        Manage
                    </button>
                </div>
            </div>
        `).join('');
    }

    /**
     * Format date for display
     */
    formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * Format status for display
     */
    formatStatus(status) {
        const statusMap = {
            'active': 'Active',
            'inactive': 'Inactive',
            'trial': 'Trial',
            'suspended': 'Suspended',
            'expired': 'Expired'
        };
        return statusMap[status] || status;
    }

    /**
     * Access instance
     */
    async accessInstance(instanceId) {
        try {
            const instance = this.state.instances.find(i => i.id === instanceId);
            if (instance) {
                // Open instance in new tab
                window.open(`https://${instance.domain}`, '_blank');
            }
        } catch (error) {
            console.error("Error accessing instance:", error);
            this.notification.add("Error accessing instance", { type: "danger" });
        }
    }

    /**
     * Manage instance
     */
    async manageInstance(instanceId) {
        try {
            // Redirect to instance management page
            window.location.href = `/saas/portal/instance/${instanceId}`;
        } catch (error) {
            console.error("Error managing instance:", error);
            this.notification.add("Error managing instance", { type: "danger" });
        }
    }

    /**
     * Manage subscription
     */
    async manageSubscription(subscriptionId) {
        try {
            // Redirect to subscription management page
            window.location.href = `/saas/portal/subscription/${subscriptionId}`;
        } catch (error) {
            console.error("Error managing subscription:", error);
            this.notification.add("Error managing subscription", { type: "danger" });
        }
    }

    /**
     * Create new instance
     */
    async createInstance() {
        try {
            // Redirect to pricing page
            window.location.href = '/saas/pricing';
        } catch (error) {
            console.error("Error creating instance:", error);
            this.notification.add("Error creating instance", { type: "danger" });
        }
    }

    /**
     * Refresh portal data
     */
    async refreshData() {
        await this.loadPortalData();
        this.notification.add("Portal data refreshed", { type: "success" });
    }
}

/**
 * SaaS Portal Application
 * Main application class for portal functionality
 */
class SaasPortalApp {
    constructor() {
        this.component = null;
        this.init();
    }

    async init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeApp());
        } else {
            this.initializeApp();
        }
    }

    initializeApp() {
        // Initialize portal component if portal section exists
        const portalSection = document.querySelector('.saas-portal-container');
        if (portalSection) {
            this.component = new SaasPortalComponent();
            this.attachEventListeners();
        }
    }

    attachEventListeners() {
        // Refresh button
        const refreshBtn = document.querySelector('.saas-refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshData());
        }

        // Create instance button
        const createBtn = document.querySelector('.saas-create-instance-btn');
        if (createBtn) {
            createBtn.addEventListener('click', () => this.createInstance());
        }
    }

    async accessInstance(instanceId) {
        if (this.component) {
            await this.component.accessInstance(instanceId);
        }
    }

    async manageInstance(instanceId) {
        if (this.component) {
            await this.component.manageInstance(instanceId);
        }
    }

    async manageSubscription(subscriptionId) {
        if (this.component) {
            await this.component.manageSubscription(subscriptionId);
        }
    }

    async createInstance() {
        if (this.component) {
            await this.component.createInstance();
        }
    }

    async refreshData() {
        if (this.component) {
            await this.component.refreshData();
        }
    }
}

// Initialize the application
const saasPortal = new SaasPortalApp();

// Export for global access
window.saasPortal = saasPortal;

// Register component for Odoo
registry.category("public_components").add("saas_portal", SaasPortalComponent);
