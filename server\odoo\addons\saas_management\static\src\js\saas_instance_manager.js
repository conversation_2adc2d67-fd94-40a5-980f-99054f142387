/** @odoo-module **/

import { Component, useState, onMounted } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";

/**
 * SaaS Instance Manager Component
 * Handles instance creation, management, and monitoring
 */
class SaasInstanceManagerComponent extends Component {
    setup() {
        this.rpc = useService("rpc");
        this.notification = useService("notification");
        this.dialog = useService("dialog");
        this.action = useService("action");
        
        this.state = useState({
            instances: [],
            servers: [],
            plans: [],
            clients: [],
            loading: true,
            creating: false,
            selectedInstance: null,
            filters: {
                status: 'all',
                plan: 'all',
                server: 'all'
            },
            newInstance: {
                name: '',
                client_id: null,
                plan_id: null,
                server_id: null,
                domain: '',
                odoo_version: '18.0'
            }
        });

        onMounted(async () => {
            await this.loadManagerData();
        });
    }

    /**
     * Load manager data from backend
     */
    async loadManagerData() {
        try {
            this.state.loading = true;
            
            const [instances, servers, plans, clients] = await Promise.all([
                this.loadInstances(),
                this.loadServers(),
                this.loadPlans(),
                this.loadClients()
            ]);
            
            this.state.instances = instances;
            this.state.servers = servers;
            this.state.plans = plans;
            this.state.clients = clients;
            
        } catch (error) {
            console.error("Error loading manager data:", error);
            this.loadDemoData();
        } finally {
            this.state.loading = false;
            this.updateManagerDisplay();
        }
    }

    /**
     * Load instances from backend
     */
    async loadInstances() {
        try {
            return await this.rpc("/web/dataset/call_kw", {
                model: "saas.instance",
                method: "search_read",
                args: [[]],
                kwargs: {
                    fields: ["name", "client_id", "plan_id", "server_id", "domain", "status", "created_date", "odoo_version"]
                }
            });
        } catch (error) {
            console.error("Error loading instances:", error);
            return this.getDemoInstances();
        }
    }

    /**
     * Load servers from backend
     */
    async loadServers() {
        try {
            return await this.rpc("/web/dataset/call_kw", {
                model: "saas.server",
                method: "search_read",
                args: [[]],
                kwargs: {
                    fields: ["name", "ip_address", "status", "max_instances", "current_instances"]
                }
            });
        } catch (error) {
            console.error("Error loading servers:", error);
            return this.getDemoServers();
        }
    }

    /**
     * Load plans from backend
     */
    async loadPlans() {
        try {
            return await this.rpc("/web/dataset/call_kw", {
                model: "saas.plan",
                method: "search_read",
                args: [[]],
                kwargs: {
                    fields: ["name", "max_users", "storage_limit", "monthly_price"]
                }
            });
        } catch (error) {
            console.error("Error loading plans:", error);
            return this.getDemoPlans();
        }
    }

    /**
     * Load clients from backend
     */
    async loadClients() {
        try {
            return await this.rpc("/web/dataset/call_kw", {
                model: "saas.client",
                method: "search_read",
                args: [[]],
                kwargs: {
                    fields: ["name", "email", "company_name"]
                }
            });
        } catch (error) {
            console.error("Error loading clients:", error);
            return this.getDemoClients();
        }
    }

    /**
     * Load demo data when backend is not available
     */
    loadDemoData() {
        this.state.instances = this.getDemoInstances();
        this.state.servers = this.getDemoServers();
        this.state.plans = this.getDemoPlans();
        this.state.clients = this.getDemoClients();
    }

    /**
     * Get demo instances
     */
    getDemoInstances() {
        return [
            {
                id: 1,
                name: "Acme Corp Production",
                client_id: [1, "Acme Corporation"],
                plan_id: [2, "Professional"],
                server_id: [1, "Server 1"],
                domain: "acme.saas-demo.com",
                status: "active",
                created_date: "2024-01-15",
                odoo_version: "18.0"
            },
            {
                id: 2,
                name: "TechStart Development",
                client_id: [2, "TechStart Ltd"],
                plan_id: [1, "Starter"],
                server_id: [1, "Server 1"],
                domain: "techstart-dev.saas-demo.com",
                status: "trial",
                created_date: "2024-01-18",
                odoo_version: "17.0"
            }
        ];
    }

    /**
     * Get demo servers
     */
    getDemoServers() {
        return [
            {
                id: 1,
                name: "Server 1",
                ip_address: "*************",
                status: "active",
                max_instances: 50,
                current_instances: 23
            },
            {
                id: 2,
                name: "Server 2",
                ip_address: "*************",
                status: "active",
                max_instances: 50,
                current_instances: 15
            }
        ];
    }

    /**
     * Get demo plans
     */
    getDemoPlans() {
        return [
            { id: 1, name: "Starter", max_users: 5, storage_limit: 10, monthly_price: 29 },
            { id: 2, name: "Professional", max_users: 25, storage_limit: 50, monthly_price: 79 },
            { id: 3, name: "Enterprise", max_users: 0, storage_limit: 500, monthly_price: 199 }
        ];
    }

    /**
     * Get demo clients
     */
    getDemoClients() {
        return [
            { id: 1, name: "John Doe", email: "<EMAIL>", company_name: "Acme Corporation" },
            { id: 2, name: "Jane Smith", email: "<EMAIL>", company_name: "TechStart Ltd" }
        ];
    }

    /**
     * Update manager display
     */
    updateManagerDisplay() {
        this.updateInstancesList();
        this.updateFilters();
        this.updateCreateForm();
    }

    /**
     * Update instances list
     */
    updateInstancesList() {
        const container = document.querySelector('.saas-instances-table tbody');
        if (!container) return;

        const filteredInstances = this.getFilteredInstances();

        if (filteredInstances.length === 0) {
            container.innerHTML = `
                <tr>
                    <td colspan="7" class="text-center">
                        <div class="saas-empty-state">
                            <p>No instances found matching the current filters.</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        container.innerHTML = filteredInstances.map(instance => `
            <tr data-instance-id="${instance.id}">
                <td>${instance.name}</td>
                <td>${instance.client_id[1]}</td>
                <td>${instance.plan_id[1]}</td>
                <td><a href="https://${instance.domain}" target="_blank">${instance.domain}</a></td>
                <td><span class="saas-status ${instance.status}">${this.formatStatus(instance.status)}</span></td>
                <td>${this.formatDate(instance.created_date)}</td>
                <td>
                    <div class="saas-instance-actions">
                        <button class="saas-btn saas-btn-primary saas-btn-small" onclick="saasInstanceManager.accessInstance(${instance.id})">
                            Access
                        </button>
                        <button class="saas-btn saas-btn-secondary saas-btn-small" onclick="saasInstanceManager.editInstance(${instance.id})">
                            Edit
                        </button>
                        <button class="saas-btn saas-btn-danger saas-btn-small" onclick="saasInstanceManager.deleteInstance(${instance.id})">
                            Delete
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * Get filtered instances
     */
    getFilteredInstances() {
        return this.state.instances.filter(instance => {
            if (this.state.filters.status !== 'all' && instance.status !== this.state.filters.status) {
                return false;
            }
            if (this.state.filters.plan !== 'all' && instance.plan_id[0] !== parseInt(this.state.filters.plan)) {
                return false;
            }
            if (this.state.filters.server !== 'all' && instance.server_id[0] !== parseInt(this.state.filters.server)) {
                return false;
            }
            return true;
        });
    }

    /**
     * Update filters
     */
    updateFilters() {
        // Update plan filter options
        const planFilter = document.getElementById('plan-filter');
        if (planFilter) {
            planFilter.innerHTML = `
                <option value="all">All Plans</option>
                ${this.state.plans.map(plan => 
                    `<option value="${plan.id}">${plan.name}</option>`
                ).join('')}
            `;
        }

        // Update server filter options
        const serverFilter = document.getElementById('server-filter');
        if (serverFilter) {
            serverFilter.innerHTML = `
                <option value="all">All Servers</option>
                ${this.state.servers.map(server => 
                    `<option value="${server.id}">${server.name}</option>`
                ).join('')}
            `;
        }
    }

    /**
     * Update create form
     */
    updateCreateForm() {
        // Update client options
        const clientSelect = document.getElementById('new-instance-client');
        if (clientSelect) {
            clientSelect.innerHTML = `
                <option value="">Select Client</option>
                ${this.state.clients.map(client => 
                    `<option value="${client.id}">${client.name} (${client.company_name})</option>`
                ).join('')}
            `;
        }

        // Update plan options
        const planSelect = document.getElementById('new-instance-plan');
        if (planSelect) {
            planSelect.innerHTML = `
                <option value="">Select Plan</option>
                ${this.state.plans.map(plan => 
                    `<option value="${plan.id}">${plan.name} - $${plan.monthly_price}/month</option>`
                ).join('')}
            `;
        }

        // Update server options
        const serverSelect = document.getElementById('new-instance-server');
        if (serverSelect) {
            serverSelect.innerHTML = `
                <option value="">Select Server</option>
                ${this.state.servers.filter(server => server.status === 'active').map(server => 
                    `<option value="${server.id}">${server.name} (${server.current_instances}/${server.max_instances})</option>`
                ).join('')}
            `;
        }
    }

    /**
     * Handle filter change
     */
    onFilterChange(filterType, value) {
        this.state.filters[filterType] = value;
        this.updateInstancesList();
    }

    /**
     * Create new instance
     */
    async createInstance() {
        try {
            this.state.creating = true;
            
            // Validate form
            if (!this.validateCreateForm()) {
                return;
            }

            // Create instance via RPC
            const instanceId = await this.rpc("/web/dataset/call_kw", {
                model: "saas.instance",
                method: "create",
                args: [this.state.newInstance],
                kwargs: {}
            });

            this.notification.add("Instance created successfully!", { type: "success" });
            
            // Reload instances
            await this.loadInstances();
            this.updateInstancesList();
            
            // Reset form
            this.resetCreateForm();
            
        } catch (error) {
            console.error("Error creating instance:", error);
            this.notification.add("Error creating instance", { type: "danger" });
        } finally {
            this.state.creating = false;
        }
    }

    /**
     * Validate create form
     */
    validateCreateForm() {
        const required = ['name', 'client_id', 'plan_id', 'server_id', 'domain'];
        
        for (const field of required) {
            if (!this.state.newInstance[field]) {
                this.notification.add(`Please fill in ${field.replace('_', ' ')}`, { type: "warning" });
                return false;
            }
        }
        
        return true;
    }

    /**
     * Reset create form
     */
    resetCreateForm() {
        this.state.newInstance = {
            name: '',
            client_id: null,
            plan_id: null,
            server_id: null,
            domain: '',
            odoo_version: '18.0'
        };
        
        // Reset form inputs
        const form = document.getElementById('create-instance-form');
        if (form) {
            form.reset();
        }
    }

    /**
     * Access instance
     */
    async accessInstance(instanceId) {
        try {
            const instance = this.state.instances.find(i => i.id === instanceId);
            if (instance) {
                window.open(`https://${instance.domain}`, '_blank');
            }
        } catch (error) {
            console.error("Error accessing instance:", error);
            this.notification.add("Error accessing instance", { type: "danger" });
        }
    }

    /**
     * Edit instance
     */
    async editInstance(instanceId) {
        try {
            this.action.doAction({
                type: 'ir.actions.act_window',
                res_model: 'saas.instance',
                res_id: instanceId,
                view_mode: 'form',
                views: [[false, 'form']],
                target: 'current',
            });
        } catch (error) {
            console.error("Error editing instance:", error);
            this.notification.add("Error editing instance", { type: "danger" });
        }
    }

    /**
     * Delete instance
     */
    async deleteInstance(instanceId) {
        try {
            const instance = this.state.instances.find(i => i.id === instanceId);
            if (!instance) return;

            const confirmed = confirm(`Are you sure you want to delete instance "${instance.name}"? This action cannot be undone.`);
            if (!confirmed) return;

            await this.rpc("/web/dataset/call_kw", {
                model: "saas.instance",
                method: "unlink",
                args: [[instanceId]],
                kwargs: {}
            });

            this.notification.add("Instance deleted successfully!", { type: "success" });
            
            // Reload instances
            await this.loadInstances();
            this.updateInstancesList();
            
        } catch (error) {
            console.error("Error deleting instance:", error);
            this.notification.add("Error deleting instance", { type: "danger" });
        }
    }

    /**
     * Format date for display
     */
    formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    /**
     * Format status for display
     */
    formatStatus(status) {
        const statusMap = {
            'active': 'Active',
            'inactive': 'Inactive',
            'trial': 'Trial',
            'suspended': 'Suspended',
            'creating': 'Creating'
        };
        return statusMap[status] || status;
    }

    /**
     * Refresh manager data
     */
    async refreshData() {
        await this.loadManagerData();
        this.notification.add("Data refreshed", { type: "success" });
    }
}

/**
 * SaaS Instance Manager Application
 */
class SaasInstanceManagerApp {
    constructor() {
        this.component = null;
        this.init();
    }

    async init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeApp());
        } else {
            this.initializeApp();
        }
    }

    initializeApp() {
        const managerSection = document.querySelector('.saas-instance-manager');
        if (managerSection) {
            this.component = new SaasInstanceManagerComponent();
            this.attachEventListeners();
        }
    }

    attachEventListeners() {
        // Filter change handlers
        const filters = ['status-filter', 'plan-filter', 'server-filter'];
        filters.forEach(filterId => {
            const filter = document.getElementById(filterId);
            if (filter) {
                filter.addEventListener('change', (e) => {
                    const filterType = filterId.replace('-filter', '');
                    this.onFilterChange(filterType, e.target.value);
                });
            }
        });

        // Create instance form
        const createForm = document.getElementById('create-instance-form');
        if (createForm) {
            createForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.createInstance();
            });
        }

        // Refresh button
        const refreshBtn = document.querySelector('.saas-refresh-instances-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshData());
        }
    }

    onFilterChange(filterType, value) {
        if (this.component) {
            this.component.onFilterChange(filterType, value);
        }
    }

    async createInstance() {
        if (this.component) {
            await this.component.createInstance();
        }
    }

    async accessInstance(instanceId) {
        if (this.component) {
            await this.component.accessInstance(instanceId);
        }
    }

    async editInstance(instanceId) {
        if (this.component) {
            await this.component.editInstance(instanceId);
        }
    }

    async deleteInstance(instanceId) {
        if (this.component) {
            await this.component.deleteInstance(instanceId);
        }
    }

    async refreshData() {
        if (this.component) {
            await this.component.refreshData();
        }
    }
}

// Initialize the application
const saasInstanceManager = new SaasInstanceManagerApp();

// Export for global access
window.saasInstanceManager = saasInstanceManager;

// Register component for Odoo
registry.category("web_components").add("saas_instance_manager", SaasInstanceManagerComponent);
