# -*- coding: utf-8 -*-

from odoo import models, fields, api


class SaasClient(models.Model):
    _name = 'saas.client'
    _description = 'Khách Hàng <PERSON>aS'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(
        string='Tên <PERSON>h<PERSON>ch Hàng',
        required=True,
        tracking=True,
        help='Tên của khách hàng SaaS'
    )

    email = fields.Char(
        string='Email',
        required=True,
        tracking=True,
        help='Địa chỉ email chính của khách hàng'
    )

    phone = fields.Char(
        string='Điện Thoại',
        tracking=True,
        help='Số điện thoại liên hệ'
    )

    company_name = fields.Char(
        string='Tên Công Ty',
        tracking=True,
        help='Tên công ty của khách hàng'
    )
    
    state = fields.Selection([
        ('draft', 'Nháp'),
        ('active', 'Hoạ<PERSON>'),
        ('suspended', '<PERSON>ạ<PERSON>'),
        ('cancelled', 'Đã <PERSON>'),
    ], string='Trạng Thái', default='draft', required=True, tracking=True)
    
    partner_id = fields.Many2one(
        'res.partner',
        string='Đối Tác Liên Quan',
        help='Bản ghi đối tác liên quan cho quản lý thanh toán và liên hệ'
    )

    plan_id = fields.Many2one(
        'saas.plan',
        string='Gói Dịch Vụ',
        tracking=True,
        help='Gói dịch vụ cho khách hàng này'
    )

    notes = fields.Text(
        string='Ghi Chú',
        help='Ghi chú bổ sung về khách hàng'
    )

    # One2many relationships
    instance_ids = fields.One2many(
        'saas.instance',
        'client_id',
        string='Instances',
        help='Các instance cơ sở dữ liệu cho khách hàng này'
    )

    active = fields.Boolean(
        string='Hoạt Động',
        default=True,
        help='Bỏ chọn để lưu trữ khách hàng'
    )

    # Computed fields
    instance_count = fields.Integer(
        string='Số Lượng Instance',
        compute='_compute_instance_count',
        help='Số lượng instance cho khách hàng này'
    )

    def _compute_instance_count(self):
        for client in self:
            client.instance_count = len(client.instance_ids)

    @api.model
    def create(self, vals):
        """Tạo đối tác liên quan khi tạo khách hàng"""
        if not vals.get('partner_id') and vals.get('name'):
            partner_vals = {
                'name': vals.get('company_name') or vals.get('name'),
                'email': vals.get('email'),
                'phone': vals.get('phone'),
                'is_company': bool(vals.get('company_name')),
                'customer_rank': 1,
            }
            partner = self.env['res.partner'].create(partner_vals)
            vals['partner_id'] = partner.id
        return super().create(vals)
    
    def write(self, vals):
        """Cập nhật đối tác liên quan khi cập nhật khách hàng"""
        result = super().write(vals)
        if self.partner_id and any(field in vals for field in ['name', 'email', 'phone', 'company_name']):
            partner_vals = {}
            if 'name' in vals or 'company_name' in vals:
                partner_vals['name'] = vals.get('company_name') or vals.get('name') or self.company_name or self.name
            if 'email' in vals:
                partner_vals['email'] = vals['email']
            if 'phone' in vals:
                partner_vals['phone'] = vals['phone']
            if partner_vals:
                self.partner_id.write(partner_vals)
        return result
    
    def action_activate(self):
        """Kích hoạt khách hàng"""
        self.write({'state': 'active'})
    
    def action_suspend(self):
        """Tạm dừng khách hàng"""
        self.write({'state': 'suspended'})

    def action_cancel(self):
        """Hủy khách hàng"""
        self.write({'state': 'cancelled'})

    def action_reset_to_draft(self):
        """Đặt lại khách hàng về trạng thái nháp"""
        self.write({'state': 'draft'})

    def action_view_instances(self):
        """Xem instances cho khách hàng này"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Instances Khách Hàng',
            'res_model': 'saas.instance',
            'view_mode': 'list,form',
            'domain': [('client_id', '=', self.id)],
            'context': {'default_client_id': self.id, 'default_plan_id': self.plan_id.id if self.plan_id else False}
        }

    # Dashboard action methods
    def action_new_client(self):
        """Tạo khách hàng mới"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Khách Hàng Mới',
            'res_model': 'saas.client',
            'view_mode': 'form',
            'target': 'new',
        }

    def action_new_instance(self):
        """Tạo instance mới"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Instance Mới',
            'res_model': 'saas.instance',
            'view_mode': 'form',
            'target': 'new',
        }

    def action_view_all_clients(self):
        """Xem tất cả khách hàng"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Tất Cả Khách Hàng',
            'res_model': 'saas.client',
            'view_mode': 'list,form',
        }

    def action_view_all_instances(self):
        """Xem tất cả instances"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Tất Cả Instances',
            'res_model': 'saas.instance',
            'view_mode': 'list,form',
        }

    # Customer portal action methods
    def action_request_new_instance(self):
        """Yêu cầu instance mới"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Yêu Cầu Instance Mới',
            'res_model': 'saas.instance',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_client_id': self.id, 'default_plan_id': self.plan_id.id if self.plan_id else False}
        }

    def action_contact_support(self):
        """Liên hệ hỗ trợ"""
        return {
            'type': 'ir.actions.act_url',
            'url': 'mailto:<EMAIL>?subject=Yêu Cầu Hỗ Trợ SaaS',
            'target': 'new',
        }

    def action_view_documentation(self):
        """Xem tài liệu"""
        return {
            'type': 'ir.actions.act_url',
            'url': 'https://docs.yourcompany.com',
            'target': 'new',
        }

    def action_view_billing(self):
        """Xem thông tin thanh toán"""
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Thanh Toán',
                'message': 'Chức năng thanh toán sẽ được triển khai trong các phiên bản tương lai.',
                'type': 'info',
            }
        }

    def action_upgrade_plan(self):
        """Nâng cấp gói"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Nâng Cấp Gói',
            'res_model': 'saas.plan',
            'view_mode': 'list,form',
            'target': 'new',
        }
