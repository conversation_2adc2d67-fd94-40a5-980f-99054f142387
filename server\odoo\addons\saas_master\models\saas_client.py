# -*- coding: utf-8 -*-

from odoo import models, fields, api


class SaasClient(models.Model):
    _name = 'saas.client'
    _description = 'SaaS Client'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'name'

    name = fields.Char(
        string='Client Name',
        required=True,
        tracking=True,
        help='Name of the SaaS client'
    )
    
    email = fields.Char(
        string='Email',
        required=True,
        tracking=True,
        help='Primary email address for the client'
    )
    
    phone = fields.Char(
        string='Phone',
        tracking=True,
        help='Contact phone number'
    )
    
    company_name = fields.Char(
        string='Company Name',
        tracking=True,
        help='Name of the client company'
    )
    
    state = fields.Selection([
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('suspended', 'Suspended'),
        ('cancelled', 'Cancelled'),
    ], string='Status', default='draft', required=True, tracking=True)
    
    partner_id = fields.Many2one(
        'res.partner',
        string='Related Partner',
        help='Related partner record for billing and contact management'
    )

    plan_id = fields.Many2one(
        'saas.plan',
        string='Service Plan',
        tracking=True,
        help='Service plan for this client'
    )

    notes = fields.Text(
        string='Notes',
        help='Additional notes about the client'
    )

    # One2many relationships
    instance_ids = fields.One2many(
        'saas.instance',
        'client_id',
        string='Instances',
        help='Database instances for this client'
    )

    active = fields.Boolean(
        string='Active',
        default=True,
        help='Uncheck to archive the client'
    )

    # Computed fields
    instance_count = fields.Integer(
        string='Instance Count',
        compute='_compute_instance_count',
        help='Number of instances for this client'
    )

    def _compute_instance_count(self):
        for client in self:
            client.instance_count = len(client.instance_ids)
    
    @api.model
    def create(self, vals):
        """Create related partner when creating client"""
        if not vals.get('partner_id') and vals.get('name'):
            partner_vals = {
                'name': vals.get('company_name') or vals.get('name'),
                'email': vals.get('email'),
                'phone': vals.get('phone'),
                'is_company': bool(vals.get('company_name')),
                'customer_rank': 1,
            }
            partner = self.env['res.partner'].create(partner_vals)
            vals['partner_id'] = partner.id
        return super().create(vals)
    
    def write(self, vals):
        """Update related partner when updating client"""
        result = super().write(vals)
        if self.partner_id and any(field in vals for field in ['name', 'email', 'phone', 'company_name']):
            partner_vals = {}
            if 'name' in vals or 'company_name' in vals:
                partner_vals['name'] = vals.get('company_name') or vals.get('name') or self.company_name or self.name
            if 'email' in vals:
                partner_vals['email'] = vals['email']
            if 'phone' in vals:
                partner_vals['phone'] = vals['phone']
            if partner_vals:
                self.partner_id.write(partner_vals)
        return result
    
    def action_activate(self):
        """Activate the client"""
        self.write({'state': 'active'})
    
    def action_suspend(self):
        """Suspend the client"""
        self.write({'state': 'suspended'})
    
    def action_cancel(self):
        """Cancel the client"""
        self.write({'state': 'cancelled'})
    
    def action_reset_to_draft(self):
        """Reset client to draft state"""
        self.write({'state': 'draft'})

    def action_view_instances(self):
        """View instances for this client"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Client Instances',
            'res_model': 'saas.instance',
            'view_mode': 'list,form',
            'domain': [('client_id', '=', self.id)],
            'context': {'default_client_id': self.id, 'default_plan_id': self.plan_id.id if self.plan_id else False}
        }

    # Dashboard action methods
    def action_new_client(self):
        """Create new client"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'New Client',
            'res_model': 'saas.client',
            'view_mode': 'form',
            'target': 'new',
        }

    def action_new_instance(self):
        """Create new instance"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'New Instance',
            'res_model': 'saas.instance',
            'view_mode': 'form',
            'target': 'new',
        }

    def action_view_all_clients(self):
        """View all clients"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'All Clients',
            'res_model': 'saas.client',
            'view_mode': 'list,form',
        }

    def action_view_all_instances(self):
        """View all instances"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'All Instances',
            'res_model': 'saas.instance',
            'view_mode': 'list,form',
        }

    # Customer portal action methods
    def action_request_new_instance(self):
        """Request new instance"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Request New Instance',
            'res_model': 'saas.instance',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_client_id': self.id, 'default_plan_id': self.plan_id.id if self.plan_id else False}
        }

    def action_contact_support(self):
        """Contact support"""
        return {
            'type': 'ir.actions.act_url',
            'url': 'mailto:<EMAIL>?subject=SaaS Support Request',
            'target': 'new',
        }

    def action_view_documentation(self):
        """View documentation"""
        return {
            'type': 'ir.actions.act_url',
            'url': 'https://docs.yourcompany.com',
            'target': 'new',
        }

    def action_view_billing(self):
        """View billing information"""
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Billing',
                'message': 'Billing functionality will be implemented in future versions.',
                'type': 'info',
            }
        }

    def action_upgrade_plan(self):
        """Upgrade plan"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Upgrade Plan',
            'res_model': 'saas.plan',
            'view_mode': 'list,form',
            'target': 'new',
        }
