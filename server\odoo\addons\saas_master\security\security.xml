<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Security Groups -->
    <record id="group_saas_user" model="res.groups">
        <field name="name">SaaS User</field>
        <field name="category_id" ref="base.module_category_operations"/>
        <field name="comment">Basic SaaS user with read access to most models</field>
    </record>

    <record id="group_saas_manager" model="res.groups">
        <field name="name">SaaS Manager</field>
        <field name="category_id" ref="base.module_category_operations"/>
        <field name="implied_ids" eval="[(4, ref('group_saas_user'))]"/>
        <field name="comment">SaaS manager with full access to manage clients, plans, instances, and servers</field>
    </record>

    <record id="group_saas_admin" model="res.groups">
        <field name="name">SaaS Administrator</field>
        <field name="category_id" ref="base.module_category_administration"/>
        <field name="implied_ids" eval="[(4, ref('group_saas_manager'))]"/>
        <field name="comment">SaaS administrator with full system access</field>
    </record>

    <!-- Record Rules will be added later if needed -->


</odoo>
