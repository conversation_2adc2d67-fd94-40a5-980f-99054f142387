# -*- coding: utf-8 -*-

from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import logging

_logger = logging.getLogger(__name__)


class SaasPlan(models.Model):
    _name = 'saas.plan'
    _description = 'SaaS Plan'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'sequence, name'

    name = fields.Char(
        string='Plan Name',
        required=True,
        tracking=True,
        help='Name of the SaaS plan'
    )
    
    code = fields.Char(
        string='Plan Code',
        required=True,
        help='Unique code for the plan'
    )
    
    description = fields.Text(
        string='Description',
        help='Detailed description of the plan'
    )
    
    sequence = fields.Integer(
        string='Sequence',
        default=10,
        help='Sequence for ordering plans'
    )
    
    active = fields.Boolean(
        string='Active',
        default=True,
        tracking=True
    )
    
    # Plan Type
    plan_type = fields.Selection([
        ('trial', 'Trial'),
        ('basic', 'Basic'),
        ('standard', 'Standard'),
        ('premium', 'Premium'),
        ('enterprise', 'Enterprise'),
        ('custom', 'Custom'),
    ], string='Plan Type', required=True, default='basic', tracking=True)
    
    # Pricing
    monthly_price = fields.Monetary(
        string='Monthly Price',
        currency_field='currency_id',
        help='Monthly subscription price'
    )
    
    yearly_price = fields.Monetary(
        string='Yearly Price',
        currency_field='currency_id',
        help='Yearly subscription price'
    )
    
    currency_id = fields.Many2one(
        'res.currency',
        string='Currency',
        default=lambda self: self.env.company.currency_id,
        required=True
    )
    
    # User Pricing
    price_per_user_monthly = fields.Monetary(
        string='Price per User (Monthly)',
        currency_field='currency_id',
        help='Additional price per user per month'
    )
    
    price_per_user_yearly = fields.Monetary(
        string='Price per User (Yearly)',
        currency_field='currency_id',
        help='Additional price per user per year'
    )
    
    # Limits
    max_users = fields.Integer(
        string='Max Users',
        default=0,
        help='Maximum number of users (0 = unlimited)'
    )
    
    max_storage_gb = fields.Integer(
        string='Max Storage (GB)',
        default=10,
        help='Maximum storage in GB'
    )
    
    max_databases = fields.Integer(
        string='Max Databases',
        default=1,
        help='Maximum number of databases'
    )
    
    # Resource Limits
    cpu_limit = fields.Float(
        string='CPU Limit (cores)',
        default=1.0,
        help='CPU limit in cores'
    )
    
    memory_limit_mb = fields.Integer(
        string='Memory Limit (MB)',
        default=1024,
        help='Memory limit in MB'
    )
    
    # Features
    backup_enabled = fields.Boolean(
        string='Backup Enabled',
        default=True,
        help='Whether backup is enabled for this plan'
    )
    
    backup_retention_days = fields.Integer(
        string='Backup Retention (Days)',
        default=30,
        help='Number of days to retain backups'
    )
    
    ssl_enabled = fields.Boolean(
        string='SSL Enabled',
        default=True,
        help='Whether SSL is enabled for this plan'
    )
    
    custom_domain_enabled = fields.Boolean(
        string='Custom Domain Enabled',
        default=False,
        help='Whether custom domains are allowed'
    )
    
    api_access_enabled = fields.Boolean(
        string='API Access Enabled',
        default=False,
        help='Whether API access is enabled'
    )
    
    support_level = fields.Selection([
        ('community', 'Community'),
        ('email', 'Email Support'),
        ('priority', 'Priority Support'),
        ('dedicated', 'Dedicated Support'),
    ], string='Support Level', default='community')
    
    # Trial Settings
    trial_duration_days = fields.Integer(
        string='Trial Duration (Days)',
        default=15,
        help='Trial duration in days'
    )
    
    trial_to_paid_conversion = fields.Boolean(
        string='Trial to Paid Conversion',
        default=True,
        help='Whether trial can be converted to paid'
    )
    
    # Apps and Modules
    included_app_ids = fields.Many2many(
        'saas.app',
        'saas_plan_app_rel',
        'plan_id',
        'app_id',
        string='Included Apps',
        help='Apps included in this plan'
    )
    
    optional_app_ids = fields.Many2many(
        'saas.app',
        'saas_plan_optional_app_rel',
        'plan_id',
        'app_id',
        string='Optional Apps',
        help='Apps that can be added to this plan'
    )
    
    # Odoo Version
    odoo_version = fields.Selection([
        ('8.0', 'Odoo 8.0'),
        ('9.0', 'Odoo 9.0'),
        ('10.0', 'Odoo 10.0'),
        ('11.0', 'Odoo 11.0'),
        ('12.0', 'Odoo 12.0'),
        ('13.0', 'Odoo 13.0'),
        ('14.0', 'Odoo 14.0'),
        ('15.0', 'Odoo 15.0'),
        ('16.0', 'Odoo 16.0'),
        ('17.0', 'Odoo 17.0'),
        ('18.0', 'Odoo 18.0'),
        ('19.0', 'Odoo 19.0'),
        ('20.0', 'Odoo 20.0'),
    ], string='Odoo Version', default='18.0', required=True)
    
    # Server Configuration
    server_id = fields.Many2one(
        'saas.server',
        string='Default Server',
        help='Default server for instances of this plan'
    )
    
    # Statistics
    instance_count = fields.Integer(
        string='Instance Count',
        compute='_compute_instance_count',
        store=True
    )
    
    active_instance_count = fields.Integer(
        string='Active Instances',
        compute='_compute_instance_count',
        store=True
    )
    
    # Product Integration
    product_template_id = fields.Many2one(
        'product.template',
        string='Product Template',
        help='Related product template for sales'
    )

    @api.depends()
    def _compute_instance_count(self):
        for plan in self:
            instances = self.env['saas.instance'].search([('plan_id', '=', plan.id)])
            plan.instance_count = len(instances)
            plan.active_instance_count = len(
                instances.filtered(lambda i: i.state == 'running')
            )

    @api.constrains('code')
    def _check_code_unique(self):
        for plan in self:
            if plan.code:
                existing = self.search([
                    ('code', '=', plan.code),
                    ('id', '!=', plan.id)
                ])
                if existing:
                    raise ValidationError(_('Plan code must be unique.'))

    @api.constrains('max_users', 'max_storage_gb', 'cpu_limit', 'memory_limit_mb')
    def _check_limits(self):
        for plan in self:
            if plan.max_storage_gb < 0:
                raise ValidationError(_('Storage limit cannot be negative.'))
            if plan.cpu_limit <= 0:
                raise ValidationError(_('CPU limit must be positive.'))
            if plan.memory_limit_mb <= 0:
                raise ValidationError(_('Memory limit must be positive.'))

    @api.model
    def create(self, vals):
        plan = super().create(vals)
        
        # Create related product template if not exists
        if not plan.product_template_id:
            plan._create_product_template()
        
        return plan

    def _create_product_template(self):
        """Create a product template for this plan"""
        product_vals = {
            'name': self.name,
            'type': 'service',
            'categ_id': self.env.ref('product.product_category_all').id,
            'list_price': self.monthly_price,
            'description': self.description,
            'sale_ok': True,
            'purchase_ok': False,
            'invoice_policy': 'order',
        }
        
        product = self.env['product.template'].create(product_vals)
        self.product_template_id = product.id

    def get_price(self, billing_period='monthly', user_count=1):
        """Calculate price for this plan"""
        if billing_period == 'monthly':
            base_price = self.monthly_price
            user_price = self.price_per_user_monthly
        else:  # yearly
            base_price = self.yearly_price
            user_price = self.price_per_user_yearly
        
        # Calculate total price
        total_price = base_price
        if user_count > 1:
            additional_users = user_count - 1
            total_price += additional_users * user_price
        
        return total_price

    def get_features_list(self):
        """Get list of features for this plan"""
        features = []
        
        if self.max_users == 0:
            features.append(_('Unlimited Users'))
        else:
            features.append(_('%d Users') % self.max_users)
        
        features.append(_('%d GB Storage') % self.max_storage_gb)
        
        if self.backup_enabled:
            features.append(_('Automatic Backups'))
        
        if self.ssl_enabled:
            features.append(_('SSL Certificate'))
        
        if self.custom_domain_enabled:
            features.append(_('Custom Domain'))
        
        if self.api_access_enabled:
            features.append(_('API Access'))
        
        features.append(_('%s Support') % dict(self._fields['support_level'].selection)[self.support_level])
        
        return features

    def action_duplicate(self):
        """Duplicate this plan"""
        copy_vals = {
            'name': _('%s (Copy)') % self.name,
            'code': '%s_copy' % self.code,
        }
        return self.copy(copy_vals)

    def action_view_instances(self):
        """View instances using this plan"""
        return {
            'type': 'ir.actions.act_window',
            'name': _('Instances'),
            'res_model': 'saas.instance',
            'view_mode': 'list,form',
            'domain': [('plan_id', '=', self.id)],
            'context': {'default_plan_id': self.id},
        }
