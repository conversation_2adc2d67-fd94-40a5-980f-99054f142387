/* SaaS Management Frontend Styles */

/* ===== PRICING SECTION ===== */
.saas-pricing-section {
    padding: 60px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.saas-pricing-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.saas-pricing-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.saas-pricing-subtitle {
    text-align: center;
    font-size: 1.2rem;
    margin-bottom: 50px;
    opacity: 0.9;
}

.saas-pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.saas-pricing-card {
    background: white;
    border-radius: 15px;
    padding: 40px 30px;
    text-align: center;
    color: #333;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.saas-pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.saas-pricing-card.featured {
    border: 3px solid #ffd700;
    transform: scale(1.05);
}

.saas-pricing-card.featured::before {
    content: "Most Popular";
    position: absolute;
    top: 20px;
    right: -30px;
    background: #ffd700;
    color: #333;
    padding: 5px 40px;
    font-weight: bold;
    font-size: 0.9rem;
    transform: rotate(45deg);
}

.saas-plan-name {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: #667eea;
}

.saas-plan-price {
    font-size: 3rem;
    font-weight: 800;
    color: #333;
    margin-bottom: 5px;
}

.saas-plan-period {
    color: #666;
    margin-bottom: 30px;
    font-size: 1rem;
}

.saas-plan-features {
    list-style: none;
    padding: 0;
    margin: 30px 0;
}

.saas-plan-features li {
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    position: relative;
    padding-left: 25px;
}

.saas-plan-features li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

.saas-plan-features li.unavailable {
    color: #999;
    text-decoration: line-through;
}

.saas-plan-features li.unavailable::before {
    content: "✗";
    color: #dc3545;
}

.saas-plan-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    margin-top: 20px;
}

.saas-plan-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* ===== CUSTOMER PORTAL ===== */
.saas-portal-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.saas-portal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    border-radius: 15px;
    margin-bottom: 30px;
    text-align: center;
}

.saas-portal-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.saas-portal-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.saas-dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    margin-bottom: 40px;
}

.saas-dashboard-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-left: 4px solid #667eea;
    transition: transform 0.3s ease;
}

.saas-dashboard-card:hover {
    transform: translateY(-5px);
}

.saas-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.saas-card-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 15px;
    font-size: 1.2rem;
}

.saas-card-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
}

.saas-card-content {
    color: #666;
    line-height: 1.6;
}

.saas-instance-list {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.saas-instance-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.saas-instance-item:last-child {
    border-bottom: none;
}

.saas-instance-info h4 {
    margin: 0 0 5px 0;
    color: #333;
    font-weight: 600;
}

.saas-instance-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.saas-instance-status {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.saas-instance-status.active {
    background: #d4edda;
    color: #155724;
}

.saas-instance-status.inactive {
    background: #f8d7da;
    color: #721c24;
}

.saas-instance-status.trial {
    background: #fff3cd;
    color: #856404;
}

/* ===== BUTTONS & FORMS ===== */
.saas-btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.saas-btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.saas-btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.saas-btn-secondary {
    background: #6c757d;
    color: white;
}

.saas-btn-success {
    background: #28a745;
    color: white;
}

.saas-btn-danger {
    background: #dc3545;
    color: white;
}

.saas-btn-small {
    padding: 8px 16px;
    font-size: 0.9rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .saas-pricing-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .saas-pricing-card.featured {
        transform: none;
    }
    
    .saas-pricing-title {
        font-size: 2rem;
    }
    
    .saas-dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .saas-instance-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.saas-fade-in {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== LOADING STATES ===== */
.saas-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.saas-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}
