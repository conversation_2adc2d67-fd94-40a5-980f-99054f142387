#!/usr/bin/env python3
import sys
import os

# Add server directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'server'))

import odoo
from odoo import api, SUPERUSER_ID

def install_saas_module():
    try:
        # Connect to database
        db_name = 'ecomplus'
        registry = odoo.registry(db_name)

        with registry.cursor() as cr:
            env = api.Environment(cr, SUPERUSER_ID, {})
            
            # Find module
            module = env['ir.module.module'].search([('name', '=', 'saas_management')])
            print(f'Module found: {module.name}, state: {module.state}')
            
            if module.state == 'uninstalled':
                print('Installing module...')
                module.button_install()
                print('Installation successful!')
            elif module.state == 'installed':
                print('Module already installed')
            else:
                print(f'Module in state: {module.state}')
                
            # Check if menu exists
            menu = env['ir.ui.menu'].search([('name', '=', 'SaaS Management')])
            if menu:
                print(f'Menu found: {menu.name}, ID: {menu.id}')
            else:
                print('Menu not found!')
                
            # Check views
            views = env['ir.ui.view'].search([('model', 'in', ['saas.client', 'saas.plan', 'saas.instance', 'saas.server'])])
            print(f'Views found: {len(views)}')
            
            # Check actions
            actions = env['ir.actions.act_window'].search([('res_model', 'in', ['saas.client', 'saas.plan', 'saas.instance', 'saas.server'])])
            print(f'Actions found: {len(actions)}')
            
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    install_saas_module()
